/**
  ******************************************************************************
  * @file    oled.c
  * <AUTHOR>
  * @version V2.0
  * @date    2024
  * @brief   I2C OLED显示模块 - 0.96寸四针脚IIC通信OLED驱动实现
  ******************************************************************************
  */

#include "oled.h"
#include "oled_font.h"
#include "../../HardWare/Delay.h"

//OLED的显存
//存放格式如下.
//[0]0 1 2 3 ... 127	
//[1]0 1 2 3 ... 127	
//[2]0 1 2 3 ... 127	
//[3]0 1 2 3 ... 127	
//[4]0 1 2 3 ... 127	
//[5]0 1 2 3 ... 127	
//[6]0 1 2 3 ... 127	
//[7]0 1 2 3 ... 127 		   
u8 OLED_GRAM[128][8];	 

//更新显存到LCD
void OLED_Refresh_Gram(void)
{
	u8 i,n;
	for(i=0;i<8;i++)
	{
		OLED_WR_Byte (0xb0+i,OLED_CMD);    //设置页地址（0~7）
		OLED_WR_Byte (0x00,OLED_CMD);      //设置显示位置—列低地址
		OLED_WR_Byte (0x10,OLED_CMD);      //设置显示位置—列高地址
		// 只刷新有效的128列，确保不超出范围
		for(n=0;n<128;n++)
		{
			OLED_WR_Byte(OLED_GRAM[n][i],OLED_DATA);
		}
	}
}

//I2C起始信号
void IIC_Start(void)
{
	OLED_SDA_Set();
	OLED_SCL_Set();
	Delay_us(4);
	OLED_SDA_Clr();
	Delay_us(4);
	OLED_SCL_Clr();
}

//I2C停止信号
void IIC_Stop(void)
{
	OLED_SCL_Clr();
	OLED_SDA_Clr();
	Delay_us(4);
	OLED_SCL_Set();
	Delay_us(4);
	OLED_SDA_Set();
	Delay_us(4);
}

//等待应答信号到来
//返回值：1，接收应答失败
//        0，接收应答成功
u8 IIC_Wait_Ack(void)
{
	u8 ucErrTime=0;
	OLED_SDA_Set();
	Delay_us(1);
	OLED_SCL_Set();
	Delay_us(1);
	while(GPIO_ReadInputDataBit(GPIOB,GPIO_Pin_7))
	{
		ucErrTime++;
		if(ucErrTime>250)
		{
			IIC_Stop();
			return 1;
		}
	}
	OLED_SCL_Clr();
	return 0;
}

//I2C发送一个字节
//返回从机有无应答
//1，有应答
//0，无应答
void IIC_Send_Byte(u8 txd)
{
    u8 t;
	OLED_SCL_Clr();//拉低时钟开始数据传输
    for(t=0;t<8;t++)
    {
        if((txd&0x80)>>7)
			OLED_SDA_Set();
		else
			OLED_SDA_Clr();
        txd<<=1;
		Delay_us(2);   //对TEA5767这三个延时都是必须的
		OLED_SCL_Set();
		Delay_us(2);
		OLED_SCL_Clr();
		Delay_us(2);
    }
}

//向SSD1306写入一个字节。
//dat:要写入的数据/命令
//cmd:数据/命令标志 0,表示命令;1,表示数据;
void OLED_WR_Byte(u8 dat,u8 cmd)
{
	IIC_Start();
	IIC_Send_Byte(0x78);		//发送设备地址+写命令
	IIC_Wait_Ack();
	if(cmd)
	{
		IIC_Send_Byte(0x40);	//写数据
	}
	else
	{
		IIC_Send_Byte(0x00);	//写命令
	}
	IIC_Wait_Ack();
	IIC_Send_Byte(dat);
	IIC_Wait_Ack();
	IIC_Stop();
}

//开启OLED显示    
void OLED_Display_On(void)
{
	OLED_WR_Byte(0X8D,OLED_CMD);  //SET DCDC命令
	OLED_WR_Byte(0X14,OLED_CMD);  //DCDC ON
	OLED_WR_Byte(0XAF,OLED_CMD);  //DISPLAY ON
}

//关闭OLED显示     
void OLED_Display_Off(void)
{
	OLED_WR_Byte(0X8D,OLED_CMD);  //SET DCDC命令
	OLED_WR_Byte(0X10,OLED_CMD);  //DCDC OFF
	OLED_WR_Byte(0XAE,OLED_CMD);  //DISPLAY OFF
}		   			

//清屏函数,清完屏,整个屏幕是黑色的!和没点亮一样!!!
void OLED_Clear(void)
{
	u8 i,n;
	// 清空显存：先清列，再清页
	for(n=0;n<128;n++)
	{
		for(i=0;i<8;i++)
		{
			OLED_GRAM[n][i]=0X00;
		}
	}

	// 使用强力清屏确保彻底清除
	OLED_Force_Clear();
}

//画点 
//x:0~127
//y:0~63
//t:1 填充 0,清空				   
void OLED_DrawPoint(u8 x,u8 y,u8 t)
{
	u8 pos,bx,temp=0;
	if(x>127||y>63)return;//超出范围了.
	pos=7-y/8;
	bx=y%8;
	temp=1<<(7-bx);
	if(t)OLED_GRAM[x][pos]|=temp;
	else OLED_GRAM[x][pos]&=~temp;	    
}

//x1,y1,x2,y2 填充区域的对角坐标
//确保x1<=x2;y1<=y2 0<=x1<=127 0<=y1<=63	 	 
//dot:0,清空;1,填充	  
void OLED_Fill(u8 x1,u8 y1,u8 x2,u8 y2,u8 dot)  
{  
	u8 x,y;  
	for(x=x1;x<=x2;x++)
	{
		for(y=y1;y<=y2;y++)OLED_DrawPoint(x,y,dot);
	}													    
	OLED_Refresh_Gram();//更新显示
}

//在指定位置显示一个字符 8x16点阵
//x:0~127 (列)
//y:0~7   (页，每页8行)
void OLED_ShowChar(u8 x,u8 y,u8 chr)
{
	u8 i=0;

	if(x>119||y>6) return; //超出范围，8x16字符需要2页，留出边界

	// 获取字符字体数据
	const unsigned char* font_data = OLED_GetCharFont(chr);

	// 先清除字符区域，避免残留（清除9列，多清一列）
	for(i=0;i<9;i++)
	{
		if((x+i)<128) // 确保不超出显存范围
		{
			OLED_GRAM[x+i][y] = 0x00;
			OLED_GRAM[x+i][y+1] = 0x00;
		}
	}

	// 显示字符上半部分（第一页）
	// 向右偏移1像素来改善显示效果
	for(i=0;i<8;i++)
	{
		if((x+i+1)<128) // 确保不超出显存范围
		{
			OLED_GRAM[x+i+1][y] = font_data[i];
		}
	}

	// 显示字符下半部分（第二页）
	// 向右偏移1像素来改善显示效果
	for(i=0;i<8;i++)
	{
		if((x+i+1)<128) // 确保不超出显存范围
		{
			OLED_GRAM[x+i+1][y+1] = font_data[i+8];
		}
	}
}

//显示一个字符串
void OLED_ShowString(u8 x,u8 y,u8 *chr)
{
	u8 j=0;
	while (chr[j]!='\0')
	{
		OLED_ShowChar(x,y,chr[j]);
		x+=8;  // 每个字符宽8像素
		if(x>120){x=0;y+=2;} // 8x16字符需要跳2页
		j++;
	}
}

//显示2个数字
//x,y :起点坐标	 
//len :数字的位数
//size:字体大小
//mode:模式	0,填充模式;1,叠加模式
//num:数值(0~4294967295);	 		  
void OLED_ShowNum(u8 x,u8 y,u32 num,u8 len,u8 size)
{         	
	u8 t,temp;
	u8 enshow=0;						   
	for(t=0;t<len;t++)
	{
		temp=(num/mypow(10,len-t-1))%10;
		if(enshow==0&&t<(len-1))
		{
			if(temp==0)
			{
				OLED_ShowChar(x+(size/2)*t,y,' ');
				continue;
			}else enshow=1; 
		 	 
		}
	 	OLED_ShowChar(x+(size/2)*t,y,temp+'0'); 
	}
} 

//m^n函数
u32 mypow(u8 m,u8 n)
{
	u32 result=1;
	while(n--)result*=m;
	return result;
}

//设置坐标
void OLED_Set_Pos(unsigned char x, unsigned char y)
{
	OLED_WR_Byte(0xb0+y,OLED_CMD);
	OLED_WR_Byte(((x&0xf0)>>4)|0x10,OLED_CMD);
	OLED_WR_Byte((x&0x0f),OLED_CMD);
}

//延时函数
void Delay_50ms(unsigned int Del_50ms)
{
	unsigned int m;
	for(;Del_50ms>0;Del_50ms--)
		for(m=12000;m>0;m--);
}

void Delay_1ms(unsigned int Del_1ms)
{
	unsigned char j;
	while(Del_1ms--)
	{
		for(j=0;j<123;j++);
	}
}

//显示汉字
void OLED_ShowCHinese(u8 x,u8 y,u8 no)
{
	u8 t,adder=0;
	OLED_Set_Pos(x,y);
    for(t=0;t<16;t++)
		{
				OLED_WR_Byte(0x01,OLED_DATA);
				adder+=1;
     }
		OLED_Set_Pos(x,y+1);
    for(t=0;t<16;t++)
			{
				OLED_WR_Byte(0x01,OLED_DATA);
				adder+=1;
      }
}

//显示BMP图片128×64起始点坐标(x,y),x的范围0～127，y为页的范围0～7
void OLED_DrawBMP(unsigned char x0, unsigned char y0,unsigned char x1, unsigned char y1,unsigned char BMP[])
{
 unsigned int j=0;
 unsigned char x,y;

  if(y1%8==0) y=y1/8;
  else y=y1/8+1;
	for(y=y0;y<y1;y++)
	{
		OLED_Set_Pos(x0,y);
    for(x=x0;x<x1;x++)
	    {
	    	OLED_WR_Byte(BMP[j++],OLED_DATA);
	    }
	}
}

//填充整个屏幕
void fill_picture(unsigned char fill_Data)
{
	unsigned char m,n;
	for(m=0;m<8;m++)
	{
		OLED_WR_Byte(0xb0+m,OLED_CMD);		//page0-page1
		OLED_WR_Byte(0x00,OLED_CMD);		//low column start address
		OLED_WR_Byte(0x10,OLED_CMD);		//high column start address
		for(n=0;n<128;n++)
			{
				OLED_WR_Byte(fill_Data,OLED_DATA);
			}
	}
}



//初始化SSD1306
void OLED_Init(void)
{
	GPIO_InitTypeDef  GPIO_InitStructure;

	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOB, ENABLE);	//使能GPIOB时钟

	//GPIOB6(SCL),B7(SDA)初始化设置 - I2C协议要求开漏输出
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_6|GPIO_Pin_7;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;//普通输出模式
	GPIO_InitStructure.GPIO_OType = GPIO_OType_OD;//开漏输出 (I2C协议要求)
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;//50MHz
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;//上拉
	GPIO_Init(GPIOB, &GPIO_InitStructure);//初始化
	
	OLED_SDA_Set();
	OLED_SCL_Set();	
	
	Delay_ms(800);

	// 首先关闭显示，清除可能的上电残留
	OLED_WR_Byte(0xAE,OLED_CMD);//--turn off oled panel

	// 立即进行一次基础清屏，清除上电时的随机内容
	OLED_WR_Byte(0x20,OLED_CMD);  // 设置内存地址模式
	OLED_WR_Byte(0x00,OLED_CMD);  // 水平地址模式
	OLED_WR_Byte(0x21,OLED_CMD);  // 设置列地址
	OLED_WR_Byte(0x00,OLED_CMD);  // 列起始地址 0
	OLED_WR_Byte(0x7F,OLED_CMD);  // 列结束地址 127
	OLED_WR_Byte(0x22,OLED_CMD);  // 设置页地址
	OLED_WR_Byte(0x00,OLED_CMD);  // 页起始地址 0
	OLED_WR_Byte(0x07,OLED_CMD);  // 页结束地址 7

	// 发送清屏数据
	u16 i;
	for(i=0;i<1024;i++)
	{
		OLED_WR_Byte(0x00,OLED_DATA);
	}

	// 继续正常的初始化序列
	OLED_WR_Byte(0x00,OLED_CMD);//---set low column address
	OLED_WR_Byte(0x10,OLED_CMD);//---set high column address
	OLED_WR_Byte(0x40,OLED_CMD);//--set start line address  Set Mapping RAM Display Start Line (0x00~0x3F)
	OLED_WR_Byte(0x81,OLED_CMD);//--set contrast control register
	OLED_WR_Byte(0xCF,OLED_CMD); // Set SEG Output Current Brightness
	OLED_WR_Byte(0xA1,OLED_CMD);//--Set SEG/Column Mapping     0xa0左右反置 0xa1正常
	OLED_WR_Byte(0xC8,OLED_CMD);//Set COM/Row Scan Direction   0xc0上下反置 0xc8正常
	OLED_WR_Byte(0xA6,OLED_CMD);//--set normal display
	OLED_WR_Byte(0xA8,OLED_CMD);//--set multiplex ratio(1 to 64)
	OLED_WR_Byte(0x3f,OLED_CMD);//--1/64 duty
	OLED_WR_Byte(0xD3,OLED_CMD);//-set display offset	Shift Mapping RAM Counter (0x00~0x3F)
	OLED_WR_Byte(0x00,OLED_CMD);//-not offset
	OLED_WR_Byte(0xd5,OLED_CMD);//--set display clock divide ratio/oscillator frequency
	OLED_WR_Byte(0x80,OLED_CMD);//--set divide ratio, Set Clock as 100 Frames/Sec
	OLED_WR_Byte(0xD9,OLED_CMD);//--set pre-charge period
	OLED_WR_Byte(0xF1,OLED_CMD);//Set Pre-Charge as 15 Clocks & Discharge as 1 Clock
	OLED_WR_Byte(0xDA,OLED_CMD);//--set com pins hardware configuration
	OLED_WR_Byte(0x12,OLED_CMD);
	OLED_WR_Byte(0xDB,OLED_CMD);//--set vcomh
	OLED_WR_Byte(0x40,OLED_CMD);//Set VCOM Deselect Level
	OLED_WR_Byte(0x20,OLED_CMD);//-Set Page Addressing Mode (0x00/0x01/0x02)
	OLED_WR_Byte(0x02,OLED_CMD);//
	OLED_WR_Byte(0x8D,OLED_CMD);//--set Charge Pump enable/disable
	OLED_WR_Byte(0x14,OLED_CMD);//--set(0x10) disable
	OLED_WR_Byte(0xA4,OLED_CMD);// Disable Entire Display On (0xa4/0xa5)
	OLED_WR_Byte(0xA6,OLED_CMD);// Disable Inverse Display On (0xa6/a7)

	// 在开启显示前先进行初始清屏
	// 使用列地址模式进行彻底清屏
	OLED_WR_Byte(0x20,OLED_CMD);  // 设置内存地址模式
	OLED_WR_Byte(0x00,OLED_CMD);  // 水平地址模式

	// 设置列地址范围 (0-127)
	OLED_WR_Byte(0x21,OLED_CMD);  // 设置列地址
	OLED_WR_Byte(0x00,OLED_CMD);  // 列起始地址 0
	OLED_WR_Byte(0x7F,OLED_CMD);  // 列结束地址 127

	// 设置页地址范围 (0-7)
	OLED_WR_Byte(0x22,OLED_CMD);  // 设置页地址
	OLED_WR_Byte(0x00,OLED_CMD);  // 页起始地址 0
	OLED_WR_Byte(0x07,OLED_CMD);  // 页结束地址 7

	// 发送1024个0x00清空整个显示区域
	u16 clear_count;
	for(clear_count=0;clear_count<1024;clear_count++)
	{
		OLED_WR_Byte(0x00,OLED_DATA);
	}

	// 回到页地址模式
	OLED_WR_Byte(0x20,OLED_CMD);  // 设置内存地址模式
	OLED_WR_Byte(0x02,OLED_CMD);  // 页地址模式

	OLED_WR_Byte(0xAF,OLED_CMD);//--turn on oled panel

	// 显示开启后再次清屏，确保没有残留
	Delay_ms(50);  // 等待显示稳定
	OLED_Force_Clear();
	OLED_Set_Pos(0,0);
}  

//OLED显示测试函数 - 使用字体库
void OLED_Test_Display(void)
{
    // 清屏
    OLED_Clear();

    // 简单测试：显示单个字符
    OLED_ShowChar(8,0,'A');  // 在第8列显示字符A
    OLED_ShowChar(16,0,'B'); // 在第16列显示字符B
    OLED_ShowChar(24,0,'C'); // 在第24列显示字符C

    OLED_Refresh_Gram();
    Delay_ms(2000);

    // 清屏并显示字符串测试
    OLED_Clear();
    OLED_ShowString(8,0,(u8*)"HELLO");
    OLED_ShowString(8,2,(u8*)"WORLD");
    OLED_ShowString(8,4,(u8*)"12345");
    OLED_ShowString(8,6,(u8*)"OLED");

    OLED_Refresh_Gram();
    Delay_ms(3000);

    // 清屏
    OLED_Clear();
}

//OLED简单显示测试 - 用于调试显示问题
void OLED_Simple_Test(void)
{
    u8 i,j;

    // 强力清屏，确保没有残留
    OLED_Force_Clear();

    // 测试1：显示简单图案
    // 在第一行显示几个点
    for(i=10;i<50;i+=10)
    {
        OLED_GRAM[i][0] = 0xFF; // 第一页全亮
    }

    // 在第二行显示几个点
    for(i=10;i<50;i+=10)
    {
        OLED_GRAM[i][1] = 0xFF; // 第二页全亮
    }

    OLED_Refresh_Gram();
    Delay_ms(2000);

    // 测试2：清屏并显示边界测试
    OLED_Force_Clear();

    // 显示屏幕边界
    OLED_GRAM[0][0] = 0xFF;   // 左上角
    OLED_GRAM[127][0] = 0xFF; // 右上角
    OLED_GRAM[0][7] = 0xFF;   // 左下角
    OLED_GRAM[127][7] = 0xFF; // 右下角

    // 显示中间的十字
    for(i=60;i<68;i++)
    {
        OLED_GRAM[i][3] = 0xFF; // 水平线
        OLED_GRAM[i][4] = 0xFF;
    }
    for(j=2;j<6;j++)
    {
        OLED_GRAM[64][j] = 0xFF; // 垂直线
    }

    OLED_Refresh_Gram();
    Delay_ms(2000);

    // 测试3：字符显示测试
    OLED_Force_Clear();

    // 在不同位置显示字符，检查对齐
    OLED_ShowChar(0,0,'A');   // 最左边
    OLED_ShowChar(8,0,'B');   // 第二个字符
    OLED_ShowChar(16,0,'C');  // 第三个字符
    OLED_ShowChar(64,2,'X');  // 中间位置
    OLED_ShowChar(112,6,'Z'); // 接近右边

    OLED_Refresh_Gram();
    Delay_ms(3000);

    OLED_Force_Clear();
}

//OLED诊断测试函数 - 用于检测硬件连接问题
void OLED_Diagnostic_Test(void)
{
    // 1. GPIO引脚测试
    // 测试SCL引脚
    OLED_SCL_Set();
    Delay_ms(500);
    OLED_SCL_Clr();
    Delay_ms(500);
    OLED_SCL_Set();

    // 测试SDA引脚
    OLED_SDA_Set();
    Delay_ms(500);
    OLED_SDA_Clr();
    Delay_ms(500);
    OLED_SDA_Set();

    // 2. 基础I2C通信测试
    // 发送设备地址测试
    IIC_Start();
    IIC_Send_Byte(0x78);  // OLED设备地址
    u8 ack_result = IIC_Wait_Ack();
    IIC_Stop();

    if(ack_result == 0)
    {
        // 通信成功，尝试发送显示命令
        OLED_WR_Byte(0xAF,OLED_CMD);  // 开启显示
        Delay_ms(100);

        // 尝试清屏
        OLED_WR_Byte(0x20,OLED_CMD);  // 设置内存地址模式
        OLED_WR_Byte(0x00,OLED_CMD);  // 水平地址模式

        // 设置页地址
        OLED_WR_Byte(0x21,OLED_CMD);  // 设置列地址
        OLED_WR_Byte(0x00,OLED_CMD);  // 列起始地址
        OLED_WR_Byte(0x7F,OLED_CMD);  // 列结束地址

        OLED_WR_Byte(0x22,OLED_CMD);  // 设置页地址
        OLED_WR_Byte(0x00,OLED_CMD);  // 页起始地址
        OLED_WR_Byte(0x07,OLED_CMD);  // 页结束地址

        // 发送测试数据
        u16 i;
        for(i=0;i<1024;i++)  // 128*8 = 1024字节
        {
            OLED_WR_Byte(0xFF,OLED_DATA);  // 全亮
        }

        Delay_ms(2000);

        // 清屏
        for(i=0;i<1024;i++)
        {
            OLED_WR_Byte(0x00,OLED_DATA);  // 全灭
        }
    }
}

//强力清屏函数 - 使用多种方法彻底清屏
void OLED_Force_Clear(void)
{
    u8 i,n;

    // 方法1：清空显存
    for(n=0;n<128;n++)
    {
        for(i=0;i<8;i++)
        {
            OLED_GRAM[n][i] = 0x00;
        }
    }

    // 方法2：使用列地址模式清屏
    OLED_WR_Byte(0x20,OLED_CMD);  // 设置内存地址模式
    OLED_WR_Byte(0x00,OLED_CMD);  // 水平地址模式

    // 设置列地址范围 (0-127)
    OLED_WR_Byte(0x21,OLED_CMD);  // 设置列地址
    OLED_WR_Byte(0x00,OLED_CMD);  // 列起始地址 0
    OLED_WR_Byte(0x7F,OLED_CMD);  // 列结束地址 127

    // 设置页地址范围 (0-7)
    OLED_WR_Byte(0x22,OLED_CMD);  // 设置页地址
    OLED_WR_Byte(0x00,OLED_CMD);  // 页起始地址 0
    OLED_WR_Byte(0x07,OLED_CMD);  // 页结束地址 7

    // 发送1024个0x00 (128列 × 8页)
    for(i=0;i<1024;i++)
    {
        OLED_WR_Byte(0x00,OLED_DATA);
    }

    // 方法3：额外清理可能的残留区域
    // 重新设置列地址，扩展范围
    OLED_WR_Byte(0x21,OLED_CMD);  // 设置列地址
    OLED_WR_Byte(0x00,OLED_CMD);  // 列起始地址 0
    OLED_WR_Byte(0x7F,OLED_CMD);  // 列结束地址 127

    // 再次发送清屏数据
    for(i=0;i<1024;i++)
    {
        OLED_WR_Byte(0x00,OLED_DATA);
    }

    // 方法4：回到页地址模式（兼容原有代码）
    OLED_WR_Byte(0x20,OLED_CMD);  // 设置内存地址模式
    OLED_WR_Byte(0x02,OLED_CMD);  // 页地址模式
}

# 陶晶池串口屏HMI设计指导 - 电赛G题专用

## 🎯 设计目标

为电赛G题设计一个专业的信号发生器和电路识别系统界面，支持：
1. **频率和峰峰值调整** - 精确控制AD9910输出
2. **电路模型识别** - 自动识别未知电路并输出模型参数
3. **用户友好界面** - 直观的操作流程和清晰的状态显示

## 📱 界面布局设计

### Page0 - 主控制界面 (480×320)
```
┌─────────────────────────────────────────────────────────────┐
│                    AD9910信号发生器                    [t0] │
├─────────────────────────────────────────────────────────────┤
│ 频率设置: [n0] 5.000    [t1] MHz                           │
│ 当前输出: [t2] 5.000 MHz                                   │
├─────────────────────────────────────────────────────────────┤
│ 幅度设置: [n1] 500      [t3] mV                            │
│ 当前输出: [t4] 500 mV                                      │
├─────────────────────────────────────────────────────────────┤
│ [b0] 应用设置    [b1] 识别电路    [b2] 预设参数             │
└─────────────────────────────────────────────────────────────┘
```

### Page1 - 电路识别界面 (480×320)
```
┌─────────────────────────────────────────────────────────────┐
│                    电路模型识别                        [t5] │
├─────────────────────────────────────────────────────────────┤
│ 扫频范围: [n2] 1       到 [n3] 100     [t6] kHz           │
│ 扫频点数: [n4] 50      步进时间: [n5] 100  ms              │
├─────────────────────────────────────────────────────────────┤
│ 识别状态: [t7] 等待开始...                                 │
│ 电路类型: [t8] 未知                                        │
│ 模型参数: [t9] --                                          │
├─────────────────────────────────────────────────────────────┤
│ [b3] 开始识别    [b4] 停止识别    [b5] 返回主页             │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 控件配置详细说明

### Page0 控件配置
| 控件ID | 类型 | 位置(x,y,w,h) | 属性设置 | 功能说明 |
|--------|------|---------------|----------|----------|
| t0 | 文本 | (120,10,240,30) | 居中对齐,字体24 | 标题显示 |
| n0 | 数字 | (100,50,80,30) | 范围1-999,默认5 | 频率数值输入 |
| t1 | 文本 | (190,50,50,30) | 左对齐,字体16 | 频率单位"MHz" |
| t2 | 文本 | (100,80,200,25) | 左对齐,字体14 | 当前频率显示 |
| n1 | 数字 | (100,110,80,30) | 范围10-5000,默认500 | 幅度数值输入 |
| t3 | 文本 | (190,110,50,30) | 左对齐,字体16 | 幅度单位"mV" |
| t4 | 文本 | (100,140,200,25) | 左对齐,字体14 | 当前幅度显示 |
| b0 | 按钮 | (50,180,100,40) | 文本"应用设置" | 应用参数设置 |
| b1 | 按钮 | (170,180,100,40) | 文本"识别电路" | 进入识别界面 |
| b2 | 按钮 | (290,180,100,40) | 文本"预设参数" | 快速参数设置 |

### Page1 控件配置
| 控件ID | 类型 | 位置(x,y,w,h) | 属性设置 | 功能说明 |
|--------|------|---------------|----------|----------|
| t5 | 文本 | (120,10,240,30) | 居中对齐,字体24 | 标题显示 |
| n2 | 数字 | (80,50,60,30) | 范围1-1000,默认1 | 扫频起始频率 |
| n3 | 数字 | (180,50,60,30) | 范围1-1000,默认100 | 扫频结束频率 |
| t6 | 文本 | (250,50,40,30) | 左对齐,字体16 | 扫频单位"kHz" |
| n4 | 数字 | (80,80,60,30) | 范围10-200,默认50 | 扫频点数 |
| n5 | 数字 | (200,80,60,30) | 范围10-1000,默认100 | 步进时间ms |
| t7 | 文本 | (50,110,380,25) | 左对齐,字体14 | 识别状态显示 |
| t8 | 文本 | (50,135,380,25) | 左对齐,字体14 | 电路类型显示 |
| t9 | 文本 | (50,160,380,25) | 左对齐,字体12 | 模型参数显示 |
| b3 | 按钮 | (50,200,100,40) | 文本"开始识别" | 开始电路识别 |
| b4 | 按钮 | (170,200,100,40) | 文本"停止识别" | 停止电路识别 |
| b5 | 按钮 | (290,200,100,40) | 文本"返回主页" | 返回主控界面 |

## 🎨 HMI编辑器设置步骤

### 1. 创建新项目
```
1. 打开陶晶池HMI编辑器
2. 新建项目 → 选择屏幕尺寸 480×320
3. 设置串口参数: 115200,8,N,1
4. 项目名称: "AD9910_CircuitID"
```

### 2. Page0设置步骤
```
1. 添加文本控件t0:
   - 位置: x=120, y=10, w=240, h=30
   - 文本: "AD9910信号发生器"
   - 字体: 24号, 居中对齐
   - 背景色: 透明

2. 添加数字控件n0:
   - 位置: x=100, y=50, w=80, h=30
   - 数值范围: 1-999
   - 默认值: 5
   - 字体: 16号
   - 触摸事件: 弹出数字键盘

3. 添加文本控件t1:
   - 位置: x=190, y=50, w=50, h=30
   - 文本: "MHz"
   - 字体: 16号, 左对齐

4. 添加文本控件t2:
   - 位置: x=100, y=80, w=200, h=25
   - 文本: "当前输出: 5.000 MHz"
   - 字体: 14号, 左对齐
   - 颜色: 蓝色

5. 重复添加n1, t3, t4控件 (幅度相关)

6. 添加按钮b0, b1, b2:
   - 尺寸: 100×40
   - 字体: 16号, 居中
   - 触摸事件: 发送对应指令
```

### 3. Page1设置步骤
```
1. 复制Page0作为模板
2. 修改标题t5为"电路模型识别"
3. 添加扫频参数控件n2, n3, t6, n4, n5
4. 添加状态显示控件t7, t8, t9
5. 添加功能按钮b3, b4, b5
```

## 📡 事件配置

### 按钮事件设置
```
b0 (应用设置): 触摸释放 → 发送 "printh 10"
b1 (识别电路): 触摸释放 → 发送 "printh 11" + "page 1"
b2 (预设参数): 触摸释放 → 发送 "printh 12"
b3 (开始识别): 触摸释放 → 发送 "printh 13"
b4 (停止识别): 触摸释放 → 发送 "printh 14"
b5 (返回主页): 触摸释放 → 发送 "printh 15" + "page 0"
```

### 数字控件事件设置
```
n0 (频率输入): 数值改变 → 发送 "printh 20,数值"
n1 (幅度输入): 数值改变 → 发送 "printh 21,数值"
n2 (起始频率): 数值改变 → 发送 "printh 22,数值"
n3 (结束频率): 数值改变 → 发送 "printh 23,数值"
n4 (扫频点数): 数值改变 → 发送 "printh 24,数值"
n5 (步进时间): 数值改变 → 发送 "printh 25,数值"
```

## 🎯 预设参数配置

### 常用频率预设
```
预设1: 1MHz / 500mV    - 低频测试
预设2: 5MHz / 500mV    - 中频测试  
预设3: 10MHz / 300mV   - 高频测试
预设4: 100kHz / 1V     - 大信号测试
```

### 扫频参数预设
```
预设A: 1-100kHz, 50点, 100ms  - 低频扫描
预设B: 100kHz-1MHz, 100点, 50ms - 中频扫描
预设C: 1-10MHz, 200点, 20ms    - 高频扫描
```

## 🔍 调试和测试

### HMI编辑器测试
```
1. 使用模拟器测试所有控件响应
2. 检查事件发送是否正确
3. 验证页面切换流畅性
4. 测试数字输入范围限制
```

### 硬件连接测试
```
1. 串口连接: USART2 (PA2/PA3) ↔ 串口屏
2. 波特率: 115200
3. 发送测试指令验证通信
4. 检查控件更新是否实时
```

## 📋 完成检查清单

### HMI设计完成
- [ ] Page0布局完成
- [ ] Page1布局完成  
- [ ] 所有控件属性设置正确
- [ ] 事件配置完成
- [ ] 预设参数配置完成
- [ ] 模拟器测试通过

### 代码集成准备
- [ ] 控件ID定义完成
- [ ] 事件处理函数准备
- [ ] 显示更新函数准备
- [ ] AD9910控制接口准备
- [ ] 电路识别算法接口准备

---

**设计完成后，我将为您生成完整的配套代码！**

# 电赛G题HMI完整实现方案

## 🎯 项目概述

为电赛G题设计的专业信号发生器和电路识别系统，包含：
- **AD9910信号发生器控制** - 精确的频率和幅度调整
- **电路模型识别** - 自动识别未知电路并输出模型参数
- **用户友好界面** - 直观的操作流程和清晰的状态显示

## 📱 界面设计完成

### ✅ Page0 - 主控制界面
```
┌─────────────────────────────────────────────────────────────┐
│                    AD9910信号发生器                    [t0] │
├─────────────────────────────────────────────────────────────┤
│ 频率设置: [n0] 5       [t1] MHz                            │
│ 当前输出: [t2] 当前输出: 5.000 MHz                         │
├─────────────────────────────────────────────────────────────┤
│ 幅度设置: [n1] 500     [t3] mV                             │
│ 当前输出: [t4] 当前输出: 500 mV                            │
├─────────────────────────────────────────────────────────────┤
│ [b0] 应用设置    [b1] 识别电路    [b2] 预设参数             │
└─────────────────────────────────────────────────────────────┘
```

### ✅ Page1 - 电路识别界面
```
┌─────────────────────────────────────────────────────────────┐
│                    电路模型识别                        [t5] │
├─────────────────────────────────────────────────────────────┤
│ 扫频范围: [n2] 1       到 [n3] 100     [t6] kHz           │
│ 扫频点数: [n4] 50      步进时间: [n5] 100  ms              │
├─────────────────────────────────────────────────────────────┤
│ 识别状态: [t7] 识别状态: 等待开始...                       │
│ 电路类型: [t8] 电路类型: 未知                              │
│ 模型参数: [t9] 模型参数: --                                │
├─────────────────────────────────────────────────────────────┤
│ [b3] 开始识别    [b4] 停止识别    [b5] 返回主页             │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 代码实现完成

### ✅ 核心功能函数
```c
/* 界面显示函数 */
void TJC_HMI_ShowMainPage(void);           // 显示主控制界面
void TJC_HMI_ShowIdentifyPage(void);       // 显示电路识别界面
void TJC_HMI_SetCurrentFrequency(uint32_t frequency_hz);
void TJC_HMI_SetCurrentAmplitude(uint16_t amplitude_mv);
void TJC_HMI_SetIdentifyStatus(const char* status);
void TJC_HMI_SetCircuitType(const char* circuit_type);
void TJC_HMI_SetModelParams(const char* model_params);

/* 输入处理函数 */
void TJC_HMI_HandleFrequencyInput(uint32_t frequency_value);
void TJC_HMI_HandleAmplitudeInput(uint16_t amplitude_value);

/* 按钮处理函数 */
void TJC_HMI_HandleApplySettings(void);    // 应用设置
void TJC_HMI_HandleStartIdentify(void);    // 开始识别
void TJC_HMI_HandlePresetParams(void);     // 预设参数
void TJC_HMI_HandleStartSweep(void);       // 开始扫频
void TJC_HMI_HandleStopSweep(void);        // 停止扫频
void TJC_HMI_HandleReturnMain(void);       // 返回主页

/* 事件处理函数 */
void TJC_HMI_HandleEvent(uint8_t event_code, uint32_t param);
```

### ✅ 事件映射表
| 事件码 | 触发条件 | 处理函数 | 功能说明 |
|--------|----------|----------|----------|
| 10 | b0按钮 | HandleApplySettings | 应用频率幅度设置 |
| 11 | b1按钮 | HandleStartIdentify | 进入电路识别界面 |
| 12 | b2按钮 | HandlePresetParams | 使用预设参数 |
| 13 | b3按钮 | HandleStartSweep | 开始扫频识别 |
| 14 | b4按钮 | HandleStopSweep | 停止扫频识别 |
| 15 | b5按钮 | HandleReturnMain | 返回主控界面 |
| 20 | n0数值变化 | HandleFrequencyInput | 频率输入处理 |
| 21 | n1数值变化 | HandleAmplitudeInput | 幅度输入处理 |
| 22-25 | n2-n5数值变化 | 扫频参数更新 | 扫频参数设置 |

## 🎨 HMI编辑器配置步骤

### 1. 创建项目
```
1. 新建项目 → 480×320屏幕
2. 串口设置: 115200,8,N,1
3. 项目名: "AD9910_CircuitID"
```

### 2. Page0控件配置
```
t0: 标题文本 (120,10,240,30) "AD9910信号发生器"
n0: 频率输入 (100,50,80,30) 范围1-420, 默认5
t1: 频率单位 (190,50,50,30) "MHz"
t2: 频率显示 (100,80,200,25) "当前输出: 5.000 MHz"
n1: 幅度输入 (100,110,80,30) 范围10-5000, 默认500
t3: 幅度单位 (190,110,50,30) "mV"
t4: 幅度显示 (100,140,200,25) "当前输出: 500 mV"
b0: 应用按钮 (50,180,100,40) "应用设置" → printh 10
b1: 识别按钮 (170,180,100,40) "识别电路" → printh 11
b2: 预设按钮 (290,180,100,40) "预设参数" → printh 12
```

### 3. Page1控件配置
```
t5: 标题文本 (120,10,240,30) "电路模型识别"
n2: 起始频率 (80,50,60,30) 范围1-1000, 默认1
n3: 结束频率 (180,50,60,30) 范围1-1000, 默认100
t6: 扫频单位 (250,50,40,30) "kHz"
n4: 扫频点数 (80,80,60,30) 范围10-200, 默认50
n5: 步进时间 (200,80,60,30) 范围10-1000, 默认100
t7: 状态显示 (50,110,380,25) "识别状态: 等待开始..."
t8: 类型显示 (50,135,380,25) "电路类型: 未知"
t9: 参数显示 (50,160,380,25) "模型参数: --"
b3: 开始按钮 (50,200,100,40) "开始识别" → printh 13
b4: 停止按钮 (170,200,100,40) "停止识别" → printh 14
b5: 返回按钮 (290,200,100,40) "返回主页" → printh 15
```

### 4. 数字控件事件配置
```
n0: 数值改变 → printh 20,数值
n1: 数值改变 → printh 21,数值
n2: 数值改变 → printh 22,数值
n3: 数值改变 → printh 23,数值
n4: 数值改变 → printh 24,数值
n5: 数值改变 → printh 25,数值
```

## 🚀 集成使用示例

### main.c集成
```c
int main(void)
{
    /* 系统初始化 */
    SystemInit();
    SysTick_Init();
    BSP_Init();
    
    /* HMI初始化 */
    TJC_HMI_Init();
    TJC_HMI_ShowMainPage();
    
    /* AD9910控制系统初始化 */
    AD9910_Control_Init();
    
    /* 主循环 */
    while (1) {
        /* 处理HMI事件 */
        TJC_HMI_Process();
        
        /* 其他任务 */
        Delay_ms(10);
    }
}
```

### 串口中断处理
```c
void USART2_IRQHandler(void)
{
    if (USART_GetITStatus(USART2, USART_IT_RXNE) != RESET) {
        uint8_t data = USART_ReceiveData(USART2);
        /* 数据会被TJC_HMI_Process()自动处理 */
    }
}
```

### 事件处理示例
```c
/* 在TJC_HMI_Process()中会自动调用 */
void ProcessReceivedEvent(uint8_t event_code, uint32_t param)
{
    TJC_HMI_HandleEvent(event_code, param);
}
```

## 🧪 测试验证

### 完整功能测试
```c
void Test_Complete_System(void)
{
    /* 运行完整测试 */
    TJC_HMI_TestAllFunctions();
}
```

### 分步测试
```c
/* 1. 测试界面显示 */
TJC_HMI_ShowMainPage();
TJC_HMI_ShowIdentifyPage();

/* 2. 测试参数设置 */
TJC_HMI_HandleFrequencyInput(10);  // 10MHz
TJC_HMI_HandleAmplitudeInput(300); // 300mV
TJC_HMI_HandleApplySettings();

/* 3. 测试电路识别 */
TJC_HMI_HandleStartIdentify();
TJC_HMI_HandleStartSweep();
```

## 📋 完成检查清单

### HMI设计
- [x] Page0布局设计完成
- [x] Page1布局设计完成
- [x] 控件属性配置完成
- [x] 事件映射配置完成
- [x] 预设参数配置完成

### 代码实现
- [x] 核心显示函数完成
- [x] 输入处理函数完成
- [x] 按钮处理函数完成
- [x] 事件处理函数完成
- [x] 兼容性函数完成
- [x] 测试函数完成

### 集成准备
- [x] 头文件定义完成
- [x] 函数声明完成
- [x] 事件码定义完成
- [x] 控件ID定义完成
- [x] 使用示例完成

## 🎯 下一步行动

1. **HMI界面制作**: 按照设计指导在陶晶池HMI编辑器中制作界面
2. **代码编译测试**: 编译当前代码确保无错误
3. **硬件连接测试**: 连接串口屏测试通信
4. **功能验证**: 逐一验证所有功能
5. **电路识别算法**: 集成实际的电路识别算法

---

**状态**: ✅ HMI设计和代码实现完成  
**下一步**: 制作HMI界面文件并进行硬件测试

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增益计算验证脚本
验证在3kHz频率下，传递函数H(s)和运算放大器的总增益，
确保最终输出3V峰峰值时AD9910的所需输出幅度。
"""

import math
import cmath

def calculate_transfer_function_gain(frequency_hz):
    """
    计算传递函数H(s) = 5/(10^-8*s^2 + 3×10^-4*s + 1)在指定频率下的增益
    
    Args:
        frequency_hz: 频率 (Hz)
    
    Returns:
        增益幅度
    """
    # 传递函数参数
    DC_GAIN = 5.0
    A2 = 1e-8    # s^2系数
    A1 = 3e-4    # s系数
    A0 = 1.0     # 常数项
    
    # 计算角频率
    omega = 2.0 * math.pi * frequency_hz
    omega2 = omega * omega
    
    # 计算分母的实部和虚部
    real_part = A0 - A2 * omega2
    imag_part = A1 * omega
    
    # 计算分母的模
    denominator_magnitude = math.sqrt(real_part * real_part + imag_part * imag_part)
    
    # 计算传递函数的增益幅度
    gain_magnitude = DC_GAIN / denominator_magnitude
    
    return gain_magnitude

def calculate_transfer_function_phase(frequency_hz):
    """
    计算传递函数H(s)在指定频率下的相位
    
    Args:
        frequency_hz: 频率 (Hz)
    
    Returns:
        相位 (度)
    """
    A2 = 1e-8    # s^2系数
    A1 = 3e-4    # s系数
    A0 = 1.0     # 常数项
    
    # 计算角频率
    omega = 2.0 * math.pi * frequency_hz
    omega2 = omega * omega
    
    real_part = A0 - A2 * omega2
    imag_part = A1 * omega
    
    # 计算相位 (弧度)
    phase_rad = -math.atan2(imag_part, real_part)
    
    # 转换为度
    phase_deg = phase_rad * 180.0 / math.pi
    
    return phase_deg

def verify_gain_calculation():
    """
    验证3kHz下的增益计算
    """
    print("=" * 60)
    print("增益计算验证 - 3kHz频率下3V峰峰值输出")
    print("=" * 60)
    
    # 参数设置
    frequency_hz = 3000  # 3kHz
    opamp_gain = 6.0     # 运算放大器增益
    target_output_mv = 3000  # 3V峰峰值目标
    ad9910_max_output_mv = 800  # AD9910最大输出
    
    # 计算传递函数增益和相位
    transfer_gain = calculate_transfer_function_gain(frequency_hz)
    transfer_phase = calculate_transfer_function_phase(frequency_hz)
    
    # 计算总增益
    total_gain = transfer_gain * opamp_gain
    
    # 计算AD9910所需输出
    required_ad9910_output_mv = target_output_mv / total_gain
    
    # 验证最终输出
    calculated_final_output_mv = required_ad9910_output_mv * total_gain
    
    # 计算误差
    error_percent = abs(calculated_final_output_mv - target_output_mv) / target_output_mv * 100.0
    
    # 输出结果
    print(f"工作频率: {frequency_hz} Hz")
    print(f"目标输出: {target_output_mv} mV")
    print()
    print("增益分析:")
    print(f"  传递函数H(s)增益: {transfer_gain:.6f}")
    print(f"  传递函数H(s)相位: {transfer_phase:.2f}°")
    print(f"  运算放大器增益: {opamp_gain:.1f}")
    print(f"  总增益: {total_gain:.6f}")
    print()
    print("输出计算:")
    print(f"  AD9910所需输出: {required_ad9910_output_mv:.2f} mV")
    print(f"  计算得到的最终输出: {calculated_final_output_mv:.2f} mV")
    print(f"  误差: {error_percent:.4f}%")
    print()
    
    # 可行性检查
    print("可行性检查:")
    if required_ad9910_output_mv <= ad9910_max_output_mv:
        print(f"  ✅ AD9910输出在范围内 ({required_ad9910_output_mv:.2f} mV ≤ {ad9910_max_output_mv} mV)")
    else:
        print(f"  ❌ AD9910输出超出范围 ({required_ad9910_output_mv:.2f} mV > {ad9910_max_output_mv} mV)")
    
    if error_percent < 1.0:
        print(f"  ✅ 误差在可接受范围内 ({error_percent:.4f}% < 1%)")
    else:
        print(f"  ⚠️  误差较大 ({error_percent:.4f}% ≥ 1%)")
    
    print()
    print("=" * 60)
    
    return {
        'frequency_hz': frequency_hz,
        'transfer_gain': transfer_gain,
        'transfer_phase': transfer_phase,
        'opamp_gain': opamp_gain,
        'total_gain': total_gain,
        'target_output_mv': target_output_mv,
        'required_ad9910_output_mv': required_ad9910_output_mv,
        'calculated_final_output_mv': calculated_final_output_mv,
        'error_percent': error_percent
    }

def frequency_response_analysis():
    """
    分析传递函数的频率响应特性
    """
    print("\n传递函数频率响应分析:")
    print("-" * 40)
    
    frequencies = [100, 1000, 3000, 10000, 100000]  # Hz
    
    for freq in frequencies:
        gain = calculate_transfer_function_gain(freq)
        phase = calculate_transfer_function_phase(freq)
        gain_db = 20 * math.log10(gain)
        
        print(f"{freq:6d} Hz: 增益 = {gain:8.4f} ({gain_db:+6.2f} dB), 相位 = {phase:+7.2f}°")

if __name__ == "__main__":
    # 执行验证
    result = verify_gain_calculation()
    
    # 频率响应分析
    frequency_response_analysis()
    
    # 生成C代码配置建议
    print("\nC代码配置建议:")
    print("-" * 40)
    print(f"AD9910_Control_SetFrequency({result['frequency_hz']});")
    print(f"AD9910_Control_SetTargetAmplitude({result['target_output_mv']});")
    print(f"AD9910_Control_SetGainFactor({result['total_gain']:.6f});")

/**
  ******************************************************************************
  * @file    oled_font.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024
  * @brief   OLED字体库实现
  ******************************************************************************
  */

#include "oled_font.h"

/**
  * @brief  获取字符的字体数据
  * @param  chr: 要显示的字符
  * @retval 指向字体数据的指针
  */
const unsigned char* OLED_GetCharFont(unsigned char chr)
{
    if(chr >= 0x20 && chr <= 0x5A) // 空格到Z
    {
        return F8X16[chr - 0x20];
    }
    else
    {
        return F8X16[0]; // 返回空格字符
    }
}

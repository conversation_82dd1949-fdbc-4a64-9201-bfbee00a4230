# 双矩阵键盘功能对应表

## 📋 概述

本文档详细说明两块4x4矩阵键盘的每个按键对应的功能，以及在不同模式下的具体操作方法。

## 🎹 第一块矩阵键盘 - 数字输入键盘

**硬件连接**: PE0-PE7  
**主要功能**: 数字输入、快速调节

### 键盘布局
```
+-----+-----+-----+-----+
|  1  |  2  |  3  | F+  |
+-----+-----+-----+-----+
|  4  |  5  |  6  | F-  |
+-----+-----+-----+-----+
|  7  |  8  |  9  | A+  |
+-----+-----+-----+-----+
|  *  |  0  |  #  | A-  |
+-----+-----+-----+-----+
```

### 按键功能详表

| 按键 | 按键编码 | 主要功能 | 在不同模式下的作用 |
|------|----------|----------|-------------------|
| **1** | KEY1_1 | 数字输入1 | 频率设置模式：输入数字1<br>幅度设置模式：输入数字1<br>正常模式：无作用 |
| **2** | KEY1_2 | 数字输入2 | 频率设置模式：输入数字2<br>幅度设置模式：输入数字2<br>正常模式：无作用 |
| **3** | KEY1_3 | 数字输入3 | 频率设置模式：输入数字3<br>幅度设置模式：输入数字3<br>正常模式：无作用 |
| **4** | KEY1_4 | 数字输入4 | 频率设置模式：输入数字4<br>幅度设置模式：输入数字4<br>正常模式：无作用 |
| **5** | KEY1_5 | 数字输入5 | 频率设置模式：输入数字5<br>幅度设置模式：输入数字5<br>正常模式：无作用 |
| **6** | KEY1_6 | 数字输入6 | 频率设置模式：输入数字6<br>幅度设置模式：输入数字6<br>正常模式：无作用 |
| **7** | KEY1_7 | 数字输入7 | 频率设置模式：输入数字7<br>幅度设置模式：输入数字7<br>正常模式：无作用 |
| **8** | KEY1_8 | 数字输入8 | 频率设置模式：输入数字8<br>幅度设置模式：输入数字8<br>正常模式：无作用 |
| **9** | KEY1_9 | 数字输入9 | 频率设置模式：输入数字9<br>幅度设置模式：输入数字9<br>正常模式：无作用 |
| **0** | KEY1_0 | 数字输入0 | 频率设置模式：输入数字0<br>幅度设置模式：输入数字0<br>正常模式：无作用 |
| **\*** | KEY1_STAR | 清除输入 | 频率设置模式：清除当前输入<br>幅度设置模式：清除当前输入<br>正常模式：无作用 |
| **#** | KEY1_HASH | 确认输入 | 频率设置模式：确认频率值并返回正常模式<br>幅度设置模式：确认幅度值并返回正常模式<br>正常模式：无作用 |
| **F+** | KEY1_FREQ_UP | 频率增加 | 所有模式：频率增加100Hz<br>范围：1Hz - 100MHz |
| **F-** | KEY1_FREQ_DOWN | 频率减少 | 所有模式：频率减少100Hz<br>范围：1Hz - 100MHz |
| **A+** | KEY1_AMP_UP | 幅度增加 | 所有模式：幅度增加0.1V<br>范围：0.1V - 5.0V |
| **A-** | KEY1_AMP_DOWN | 幅度减少 | 所有模式：幅度减少0.1V<br>范围：0.1V - 5.0V |

## 🎛️ 第二块矩阵键盘 - 功能控制键盘

**硬件连接**: PE8-PE15  
**主要功能**: 模式切换、波形选择、系统控制

### 键盘布局 (简化版)
```
+-----+-----+-----+-----+
|FREQ | AMP | <<  | >>  |
+-----+-----+-----+-----+
|RSV5 |RSV6 |RSV7 |RSV8 |
+-----+-----+-----+-----+
| SIN | SQR | TRI | SAW |
+-----+-----+-----+-----+
|RSV13|RSV14|RSV15|RSV16|
+-----+-----+-----+-----+
```

### 按键功能详表

| 按键 | 按键编码 | 主要功能 | 详细说明 |
|------|----------|----------|----------|
| **FREQ** | KEY2_FREQ_SET | 频率设置模式 | 进入频率设置模式<br>显示当前频率值<br>可用数字键盘输入新值 |
| **AMP** | KEY2_AMP_SET | 幅度设置模式 | 进入幅度设置模式<br>显示当前幅度值<br>可用数字键盘输入新值 |
| **<<** | KEY2_LEFT_SHIFT | 左移位 | 数字编辑时光标左移<br>当前返回正常模式 |
| **>>** | KEY2_RIGHT_SHIFT | 右移位 | 数字编辑时光标右移<br>当前返回正常模式 |
| **RSV5-8** | KEY2_RESERVED_5-8 | 预留功能 | 预留给未来功能扩展<br>当前无作用 |
| **SIN** | KEY2_SIN | 正弦波 | 选择正弦波输出<br>立即生效 |
| **SQR** | KEY2_SQUARE | 方波 | 选择方波输出<br>立即生效 |
| **TRI** | KEY2_TRIANGLE | 三角波 | 选择三角波输出<br>立即生效 |
| **SAW** | KEY2_SAWTOOTH | 锯齿波 | 选择锯齿波输出<br>立即生效 |
| **RSV13-16** | KEY2_RESERVED_13-16 | 预留功能 | 预留给未来功能扩展<br>当前无作用 |

## 🔄 工作模式说明

### 1. 正常模式 (MODE_NORMAL)
**显示内容**:
```
SIGNAL GEN
F:1000Hz
A:1.0V
W:SIN
```

**可用按键**:
- F+/F-: 快速调节频率
- A+/A-: 快速调节幅度
- SIN/SQR/TRI/SAW: 直接切换波形
- FREQ/AMP/WAVE/MSR: 进入相应设置模式

### 2. 频率设置模式 (MODE_FREQ_SET)
**显示内容**:
```
FREQ SET
F:1000_
# CONFIRM
ESC EXIT
```

**可用按键**:
- 0-9: 输入频率数字
- *: 清除当前输入
- #: 确认频率值
- ESC: 退出不保存

### 3. 幅度设置模式 (MODE_AMP_SET)
**显示内容**:
```
AMP SET
A:1000_
# CONFIRM
ESC EXIT
```

**可用按键**:
- 0-9: 输入幅度数字(mV)
- *: 清除当前输入
- #: 确认幅度值
- ESC: 退出不保存

### 4. 波形选择模式 (MODE_WAVE_SEL)
**显示内容**:
```
WAVE SEL
W:SIN
SIN SQR TRI
ENT EXIT
```

**可用按键**:
- SIN/SQR/TRI/SAW: 直接选择波形
- ENT: 确认选择
- ESC: 退出

### 5. 测量模式 (MODE_MEASURE)
**显示内容**:
```
MEASURE
F:1000Hz
G:-3.0dB
P:45.0°
```

**可用按键**:
- ESC: 退出测量模式

## 📝 操作示例

### 示例1: 设置频率为5000Hz
1. 按 **FREQ** 进入频率设置模式
2. 按 **5**, **0**, **0**, **0** 输入5000
3. 按 **#** 确认设置
4. 系统返回正常模式，显示新频率

### 示例2: 设置幅度为2.5V
1. 按 **AMP** 进入幅度设置模式
2. 按 **2**, **5**, **0**, **0** 输入2500(mV)
3. 按 **#** 确认设置
4. 系统返回正常模式，显示新幅度

### 示例3: 快速调节频率
1. 在正常模式下直接按 **F+** 增加100Hz
2. 或按 **F-** 减少100Hz
3. 实时显示频率变化

### 示例4: 切换波形
1. 在任意模式下按 **SIN** 选择正弦波
2. 或按 **SQR** 选择方波
3. 立即生效并更新显示

## ⚠️ 注意事项

### 输入范围限制
- **频率范围**: 1Hz - 100MHz
- **幅度范围**: 100mV - 5000mV (0.1V - 5.0V)
- **超出范围的输入将被忽略**

### 按键响应
- **单次按键**: 执行一次操作
- **长按**: 当前版本不支持连续操作
- **同时按键**: 不支持组合键

### 模式切换
- **任何时候按ESC**: 返回正常模式
- **设置模式下**: 必须确认或取消才能切换
- **测量模式**: 只能通过ESC退出

## 🔧 故障排除

### 按键无响应
1. 检查PE端口连接
2. 确认矩阵键盘电源
3. 检查行列线连接

### 显示异常
1. 检查OLED连接(PB6,PB7)
2. 确认OLED电源(3.3V)
3. 重启系统

### 参数设置无效
1. 确认输入范围正确
2. 检查确认键(#)是否按下
3. 查看OLED显示是否更新

**按键功能表已完成，接下来将优化系统性能。**

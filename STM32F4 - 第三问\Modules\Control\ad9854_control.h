/**
  ******************************************************************************
  * @file    ad9854_control.h
  * <AUTHOR> - AD9854适配器
  * @version V1.0
  * @date    2024
  * @brief   AD9854控制适配器 - 提供与AD9910相同的控制接口
  ******************************************************************************
  * @attention
  * 
  * 这是一个适配器模块，将现有的AD9854驱动包装成与AD9910控制相同的接口，
  * 使得上层信号发生器控制模块可以无缝使用AD9854。
  * 
  * 功能特性：
  * - 频率控制 (1Hz - 50MHz，AD9854限制)
  * - 幅度控制 (通过外部DAC或内部控制)
  * - 波形选择 (正弦波为主，其他波形通过调制实现)
  * - 与现有AD9854驱动完全兼容
  * - 不修改任何AD9854原始代码
  *
  ******************************************************************************
  */

#ifndef __AD9854_CONTROL_H
#define __AD9854_CONTROL_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include <stdint.h>
#include <stdbool.h>

/* Exported types ------------------------------------------------------------*/

/**
  * @brief  控制状态枚举
  */
typedef enum {
    CONTROL_STATUS_OK = 0,              ///< 操作成功
    CONTROL_STATUS_ERROR = 1,           ///< 操作失败
    CONTROL_STATUS_INVALID_PARAM = 2,   ///< 参数无效
    CONTROL_STATUS_NOT_READY = 3        ///< 设备未就绪
} ControlStatus_t;

/**
  * @brief  波形类型枚举 (适配AD9854)
  */
typedef enum {
    AD9854_WAVE_SINE = 0,               ///< 正弦波 (AD9854原生支持)
    AD9854_WAVE_SQUARE = 1,             ///< 方波 (通过调制实现)
    AD9854_WAVE_TRIANGLE = 2,           ///< 三角波 (通过调制实现)
    AD9854_WAVE_SAWTOOTH = 3            ///< 锯齿波 (通过调制实现)
} AD9854_Wave_t;

/**
  * @brief  AD9854控制参数结构体
  */
typedef struct {
    uint32_t frequency_hz;              ///< 当前频率 (Hz)
    uint16_t amplitude_mv;              ///< 当前幅度 (mV)
    AD9854_Wave_t wave_type;            ///< 当前波形类型
    bool output_enabled;                ///< 输出使能状态
    bool initialized;                   ///< 初始化状态
} AD9854_Control_Params_t;

/* Exported constants --------------------------------------------------------*/

/** @defgroup AD9854_CONTROL_Exported_Constants AD9854控制导出常量
  * @{
  */

#define AD9854_FREQ_MIN_HZ              1U          ///< 最小频率 1Hz
#define AD9854_FREQ_MAX_HZ              50000000U   ///< 最大频率 50MHz (AD9854限制)
#define AD9854_FREQ_DEFAULT_HZ          5000000U    ///< 默认频率 5MHz

#define AD9854_AMP_MIN_MV               100U        ///< 最小幅度 0.1V
#define AD9854_AMP_MAX_MV               5000U       ///< 最大幅度 5.0V
#define AD9854_AMP_DEFAULT_MV           500U        ///< 默认幅度 0.5V (题目要求)

/**
  * @}
  */

/* Exported macro ------------------------------------------------------------*/

/** @defgroup AD9854_CONTROL_Exported_Macros AD9854控制导出宏
  * @{
  */

/**
  * @brief  频率范围检查
  */
#define AD9854_IS_FREQ_VALID(freq) \
    ((freq) >= AD9854_FREQ_MIN_HZ && (freq) <= AD9854_FREQ_MAX_HZ)

/**
  * @brief  幅度范围检查
  */
#define AD9854_IS_AMP_VALID(amp) \
    ((amp) >= AD9854_AMP_MIN_MV && (amp) <= AD9854_AMP_MAX_MV)

/**
  * @}
  */

/* Exported variables --------------------------------------------------------*/

extern volatile bool g_ad9854_control_initialized;    ///< 初始化标志

/* Exported functions --------------------------------------------------------*/

/** @defgroup AD9854_CONTROL_Exported_Functions AD9854控制导出函数
  * @{
  */

/**
  * @brief  AD9854控制模块初始化
  * @param  None
  * @retval CONTROL_STATUS_OK: 成功, 其他: 失败
  */
ControlStatus_t AD9854_Control_Init(void);

/**
  * @brief  AD9854控制模块反初始化
  * @param  None
  * @retval None
  */
void AD9854_Control_DeInit(void);

/**
  * @brief  设置输出频率
  * @param  frequency_hz: 频率值 (Hz)
  * @retval CONTROL_STATUS_OK: 成功, 其他: 失败
  */
ControlStatus_t AD9854_Control_SetFrequency(uint32_t frequency_hz);

/**
  * @brief  设置目标幅度 (通过外部电路调整)
  * @param  target_amplitude_mv: 目标幅度 (mV)
  * @retval CONTROL_STATUS_OK: 成功, 其他: 失败
  */
ControlStatus_t AD9854_Control_SetTargetAmplitude(uint16_t target_amplitude_mv);

/**
  * @brief  设置波形类型
  * @param  wave_type: 波形类型
  * @retval CONTROL_STATUS_OK: 成功, 其他: 失败
  */
ControlStatus_t AD9854_Control_SetWaveType(AD9854_Wave_t wave_type);

/**
  * @brief  使能/禁用输出
  * @param  enable: true-使能, false-禁用
  * @retval CONTROL_STATUS_OK: 成功, 其他: 失败
  */
ControlStatus_t AD9854_Control_EnableOutput(bool enable);

/**
  * @brief  获取当前参数
  * @param  None
  * @retval 参数结构体指针
  */
const AD9854_Control_Params_t* AD9854_Control_GetParams(void);

/**
  * @brief  获取当前频率
  * @param  None
  * @retval 当前频率 (Hz)
  */
uint32_t AD9854_Control_GetFrequency(void);

/**
  * @brief  获取当前幅度
  * @param  None
  * @retval 当前幅度 (mV)
  */
uint16_t AD9854_Control_GetAmplitude(void);

/**
  * @brief  获取当前波形类型
  * @param  None
  * @retval 当前波形类型
  */
AD9854_Wave_t AD9854_Control_GetWaveType(void);

/**
  * @brief  检查输出是否使能
  * @param  None
  * @retval true: 使能, false: 禁用
  */
bool AD9854_Control_IsOutputEnabled(void);

/**
  * @brief  检查模块是否就绪
  * @param  None
  * @retval true: 就绪, false: 未就绪
  */
bool AD9854_Control_IsReady(void);

/**
  * @brief  复位AD9854
  * @param  None
  * @retval CONTROL_STATUS_OK: 成功, 其他: 失败
  */
ControlStatus_t AD9854_Control_Reset(void);

/**
  * @brief  获取设备信息字符串
  * @param  None
  * @retval 设备信息字符串
  */
const char* AD9854_Control_GetDeviceInfo(void);

/**
  * @}
  */

#ifdef __cplusplus
}
#endif

#endif /* __AD9854_CONTROL_H */

/************************ (C) COPYRIGHT 嵌入式竞赛团队 *****END OF FILE****/

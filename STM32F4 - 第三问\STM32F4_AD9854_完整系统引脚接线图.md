# STM32F4 + AD9854 完整信号发生器系统引脚接线图

## 系统概述

本系统基于STM32F407 + AD9854 DDS芯片，实现完整的信号发生器控制系统，包含：
- AD9854 DDS信号生成模块（保持原有引脚配置）
- 双4x4矩阵键盘用户输入
- OLED显示屏实时参数显示
- 完整的控制系统软件

## 核心模块引脚分配

### 1. AD9854 DDS模块接线（**不可更改**）

| STM32引脚 | AD9854引脚 | 功能 | 说明 |
|----------|-----------|------|------|
| **控制信号** |
| PE4 | RST | 复位信号 | 高电平有效 |
| PE5 | UDCLK | 更新时钟 | 上升沿有效 |
| PE6 | WR | 写使能 | 低电平有效 |
| PB8 | RD | 读使能 | 低电平有效 |
| PB9 | OSK | 输出移位键控 | 控制输出使能 |
| PD12 | FSK | 频移键控 | 调制控制 |
| **数据总线** |
| PD0 | D0 | 数据位0 | 8位并行数据总线 |
| PD1 | D1 | 数据位1 | |
| PD2 | D2 | 数据位2 | |
| PD3 | D3 | 数据位3 | |
| PD4 | D4 | 数据位4 | |
| PD5 | D5 | 数据位5 | |
| PD6 | D6 | 数据位6 | |
| PD7 | D7 | 数据位7 | |
| **地址总线** |
| PE8 | A0 | 地址位0 | 6位地址总线 |
| PE9 | A1 | 地址位1 | |
| PE10 | A2 | 地址位2 | |
| PE11 | A3 | 地址位3 | |
| PE12 | A4 | 地址位4 | |
| PE13 | A5 | 地址位5 | |

### 2. OLED显示屏接线

| STM32引脚 | OLED引脚 | 功能 | 说明 |
|----------|----------|------|------|
| PB6 | SCL | I2C时钟线 | 硬件I2C1_SCL |
| PB7 | SDA | I2C数据线 | 硬件I2C1_SDA |
| 3.3V | VCC | 电源正极 | 3.3V供电 |
| GND | GND | 电源负极 | 共地 |

**OLED配置参数：**
- 分辨率：128x64像素
- 接口：I2C (地址0x78)
- 显示内容：频率、幅度、波形、模式

### 3. 第一块4x4矩阵键盘接线（数字输入键盘）

| STM32引脚 | 键盘引脚 | 功能 | GPIO配置 |
|----------|----------|------|----------|
| **行线（输出）** |
| PA0 | ROW0 | 第1行 | 推挽输出，默认高电平 |
| PA1 | ROW1 | 第2行 | 推挽输出，默认高电平 |
| PA3 | ROW2 | 第3行 | 推挽输出，默认高电平 |
| PA7 | ROW3 | 第4行 | 推挽输出，默认高电平 |
| **列线（输入）** |
| PA8 | COL0 | 第1列 | 上拉输入 |
| PA9 | COL1 | 第2列 | 上拉输入 |
| PA10 | COL2 | 第3列 | 上拉输入 |
| PA11 | COL3 | 第4列 | 上拉输入 |

**第一块键盘布局：**
```
+-----+-----+-----+-----+
|  1  |  2  |  3  | F+  |  F+ = 频率增加100Hz
+-----+-----+-----+-----+
|  4  |  5  |  6  | F-  |  F- = 频率减少100Hz
+-----+-----+-----+-----+
|  7  |  8  |  9  | A+  |  A+ = 幅度增加0.1V
+-----+-----+-----+-----+
|  *  |  0  |  #  | A-  |  A- = 幅度减少0.1V
+-----+-----+-----+-----+
```

### 4. 第二块4x4矩阵键盘接线（功能控制键盘）

| STM32引脚 | 键盘引脚 | 功能 | GPIO配置 |
|----------|----------|------|----------|
| **行线（输出）** |
| PC0 | ROW0 | 第1行 | 推挽输出，默认高电平 |
| PC6 | ROW1 | 第2行 | 推挽输出，默认高电平 |
| PC7 | ROW2 | 第3行 | 推挽输出，默认高电平 |
| PC9 | ROW3 | 第4行 | 推挽输出，默认高电平 |
| **列线（输入）** |
| PC11 | COL0 | 第1列 | 上拉输入 |
| PC12 | COL1 | 第2列 | 上拉输入 |
| PC14 | COL2 | 第3列 | 上拉输入 |
| PC15 | COL3 | 第4列 | 上拉输入 |

**第二块键盘布局：**
```
+-----+-----+-----+-----+
|FREQ | AMP | <<  | >>  |  FREQ=频率设置模式, AMP=幅度设置模式
+-----+-----+-----+-----+  <<=左移, >>=右移
|RSV5 |RSV6 |RSV7 |RSV8 |  RSV=预留功能
+-----+-----+-----+-----+
| SIN | SQR | TRI | SAW |  波形选择：正弦/方波/三角/锯齿
+-----+-----+-----+-----+
|RSV13|RSV14|RSV15|RSV16|  RSV=预留功能
+-----+-----+-----+-----+
```

## 系统功能说明

### 信号输出特性
- **频率范围**：1Hz - 50MHz（AD9854限制）
- **默认频率**：5MHz
- **幅度范围**：0.1V - 5.0V峰峰值
- **默认幅度**：0.5V峰峰值
- **波形类型**：正弦波（主要），方波/三角波/锯齿波（通过调制）

### 控制功能
- **实时调节**：通过矩阵键盘快速调节频率和幅度
- **精确设置**：进入设置模式进行精确数值输入
- **波形选择**：支持多种波形类型
- **实时显示**：OLED显示当前参数和状态

### 引脚冲突解决方案
原发挥部分第一问使用PE0-PE15作为矩阵键盘引脚，与AD9854的PE4-PE13冲突。
解决方案：
- **保持AD9854引脚不变**（按用户要求）
- **重新分配矩阵键盘到PA和PC端口**
- **释放大量GPIO资源供未来扩展使用**

## 可用引脚资源

### 完全可用的引脚
- **GPIOB**：PB0-PB5, PB10-PB15（除PB6,PB7,PB8,PB9外）
- **GPIOD**：PD8-PD11, PD13-PD15（除数据总线和PD12外）
- **GPIOE**：PE0-PE3, PE14-PE15（除AD9854占用外）
- **GPIOF**：PF0-PF15（全部可用）
- **GPIOG**：PG0-PG15（全部可用）
- **GPIOH**：PH0-PH1（除时钟引脚外）

### 部分可用的引脚
- **GPIOA**：PA2, PA4-PA6, PA12-PA15（除矩阵键盘占用外）
- **GPIOC**：PC1-PC5, PC8, PC10, PC13（除矩阵键盘占用外）

## 扩展接口预留

### SPI接口
- **SPI1**：PA5(SCK), PA6(MISO), PA7(MOSI) - 部分引脚被矩阵键盘占用
- **SPI2**：PB13(SCK), PB14(MISO), PB15(MOSI) - 完全可用
- **SPI3**：PC10(SCK), PC11(MISO), PC12(MOSI) - 部分引脚被矩阵键盘占用

### UART接口
- **USART1**：PA9(TX), PA10(RX) - 被矩阵键盘占用
- **USART2**：PA2(TX), PA3(RX) - PA2可用，PA3被矩阵键盘占用
- **USART3**：PB10(TX), PB11(RX) - 完全可用

### ADC接口
- **ADC1/2/3**：PA0-PA7, PB0-PB1, PC0-PC5等 - 部分可用

### 定时器接口
- 多个定时器通道可用于PWM输出、编码器接口等

## 注意事项

1. **AD9854引脚严禁修改**：按用户要求，AD9854的所有引脚配置保持不变
2. **矩阵键盘扫描**：采用行输出、列输入的扫描方式，注意消抖处理
3. **I2C上拉电阻**：OLED的I2C接口需要外部上拉电阻（通常4.7kΩ）
4. **电源供电**：确保所有模块都有稳定的3.3V供电
5. **共地连接**：所有模块必须共地，避免地线干扰
6. **信号完整性**：高频信号线要注意阻抗匹配和屏蔽

## 软件架构

### 模块层次
```
应用层：SignalGenerator_Process()
控制层：AD9854_Control_*(), MatrixKeypad_*(), OLED_*()
驱动层：AD9854_*(), GPIO_*(), I2C_*()
硬件层：STM32F407 + AD9854 + OLED + 矩阵键盘
```

### 主要功能模块
- **AD9854控制适配器**：包装现有AD9854驱动，提供统一接口
- **矩阵键盘驱动**：双键盘扫描，支持消抖和事件处理
- **OLED显示驱动**：实时参数显示和状态指示
- **信号发生器控制**：整合所有功能的主控制逻辑

此配置实现了完整的信号发生器控制系统，保持了AD9854的原有功能，同时添加了丰富的用户交互界面。

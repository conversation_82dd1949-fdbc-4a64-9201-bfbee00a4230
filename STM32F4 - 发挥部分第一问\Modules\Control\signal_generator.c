/**
  ******************************************************************************
  * @file    signal_generator.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024
  * @brief   信号发生器控制模块实现
  ******************************************************************************
  */

#include "signal_generator.h"
#include "ad9910_control.h"
#include "../Core/systick.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

/* Private typedef -----------------------------------------------------------*/

/* Private define ------------------------------------------------------------*/

/* Private macro -------------------------------------------------------------*/

/* Private variables ---------------------------------------------------------*/

/**
  * @brief  初始化标志
  */
volatile bool g_signal_generator_initialized = false;

/**
  * @brief  信号发生器参数
  */
static Signal_Generator_t s_sig_gen = {
    .frequency_hz = SIG_GEN_FREQ_DEFAULT_HZ,
    .amplitude_mv = SIG_GEN_AMP_DEFAULT_MV,
    .wave_type = WAVE_SINE,
    .work_mode = MODE_NORMAL,
    .edit_state = EDIT_STATE_NONE,
    .edit_position = 0,
    .freq_buffer = {0},
    .amp_buffer = {0},
    .buffer_index = 0,
    .parameter_changed = true,
    .display_update_needed = true
};

/**
  * @brief  测量结果
  */
static Measurement_Result_t s_measurement = {
    .input_freq_hz = 0,
    .input_amp_mv = 0,
    .phase_deg = 0.0f,
    .gain_db = 0.0f,
    .valid = false
};

/**
  * @brief  波形名称表
  */
static const char* s_wave_names[WAVE_COUNT] = {
    "SIN", "SQR", "TRI", "SAW"
};

/**
  * @brief  模式名称表
  */
static const char* s_mode_names[MODE_COUNT] = {
    "NORMAL", "FREQ", "AMP", "WAVE", "MEASURE"
};

/**
  * @brief  上次显示更新时间
  */
static uint32_t s_last_display_update = 0;

/* Private function prototypes -----------------------------------------------*/

static void SignalGenerator_ProcessKeypad1(uint8_t key_code);
static void SignalGenerator_ProcessKeypad2(uint8_t key_code);
static void SignalGenerator_UpdateParameters(void);
static void SignalGenerator_DisplayMain(void);
static void SignalGenerator_DisplayFreqSet(void);
static void SignalGenerator_DisplayAmpSet(void);
static void SignalGenerator_DisplayWaveSet(void);
static void SignalGenerator_DisplayMeasurement(void);
static bool SignalGenerator_ProcessDigitInput(char digit);
static void SignalGenerator_ClearInputBuffer(void);
static uint32_t SignalGenerator_ParseFrequency(const char* buffer);
static uint16_t SignalGenerator_ParseAmplitude(const char* buffer);

/* Exported functions --------------------------------------------------------*/

/**
  * @brief  信号发生器初始化
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t SignalGenerator_Init(void)
{
    if (g_signal_generator_initialized) {
        return 0;
    }
    
    // 初始化矩阵键盘
    if (MatrixKeypad_Init() != 0) {
        return -1;
    }
    
    // 初始化OLED显示
    // OLED_Init(); // 假设已经在main中初始化
    
    // 初始化AD9910控制
    if (AD9910_Control_Init() != 0) {
        return -1;
    }
    
    // 设置初始参数
    SignalGenerator_UpdateParameters();
    
    g_signal_generator_initialized = true;
    return 0;
}

/**
  * @brief  信号发生器反初始化
  * @param  None
  * @retval None
  */
void SignalGenerator_DeInit(void)
{
    if (!g_signal_generator_initialized) {
        return;
    }
    
    MatrixKeypad_DeInit();
    // AD9910_Control_DeInit(); // 如果有的话
    
    g_signal_generator_initialized = false;
}

/**
  * @brief  信号发生器主循环处理
  * @param  None
  * @retval None
  */
void SignalGenerator_Process(void)
{
    if (!g_signal_generator_initialized) {
        return;
    }
    
    // 扫描键盘输入
    Key_Event_t key_event;
    if (MatrixKeypad_ScanAll(&key_event)) {
        if (key_event.state == KEY_STATE_PRESSED) {
            if (key_event.keypad_id == KEYPAD_1) {
                SignalGenerator_ProcessKeypad1(key_event.key_code);
            } else if (key_event.keypad_id == KEYPAD_2) {
                SignalGenerator_ProcessKeypad2(key_event.key_code);
            }
        }
    }
    
    // 更新显示
    uint32_t current_time = SysTick_GetTick();
    if (s_sig_gen.display_update_needed || 
        (current_time - s_last_display_update) >= SIG_GEN_DISPLAY_UPDATE_MS) {
        
        SignalGenerator_UpdateDisplay();
        s_sig_gen.display_update_needed = false;
        s_last_display_update = current_time;
    }
    
    // 更新AD9910参数
    if (s_sig_gen.parameter_changed) {
        SignalGenerator_UpdateParameters();
        s_sig_gen.parameter_changed = false;
    }
}

/**
  * @brief  设置频率
  * @param  frequency_hz: 频率值 (Hz)
  * @retval true: 成功, false: 失败
  */
bool SignalGenerator_SetFrequency(uint32_t frequency_hz)
{
    if (!SIG_GEN_IS_FREQ_VALID(frequency_hz)) {
        return false;
    }
    
    s_sig_gen.frequency_hz = frequency_hz;
    s_sig_gen.parameter_changed = true;
    s_sig_gen.display_update_needed = true;
    
    return true;
}

/**
  * @brief  设置幅度
  * @param  amplitude_mv: 幅度值 (mV)
  * @retval true: 成功, false: 失败
  */
bool SignalGenerator_SetAmplitude(uint16_t amplitude_mv)
{
    if (!SIG_GEN_IS_AMP_VALID(amplitude_mv)) {
        return false;
    }
    
    s_sig_gen.amplitude_mv = amplitude_mv;
    s_sig_gen.parameter_changed = true;
    s_sig_gen.display_update_needed = true;
    
    return true;
}

/**
  * @brief  设置波形类型
  * @param  wave_type: 波形类型
  * @retval true: 成功, false: 失败
  */
bool SignalGenerator_SetWaveType(Wave_Type_t wave_type)
{
    if (wave_type >= WAVE_COUNT) {
        return false;
    }
    
    s_sig_gen.wave_type = wave_type;
    s_sig_gen.parameter_changed = true;
    s_sig_gen.display_update_needed = true;
    
    return true;
}

/**
  * @brief  获取当前参数
  * @param  None
  * @retval 信号发生器参数结构体指针
  */
const Signal_Generator_t* SignalGenerator_GetParams(void)
{
    return &s_sig_gen;
}

/**
  * @brief  开始测量模式
  * @param  None
  * @retval true: 成功, false: 失败
  */
bool SignalGenerator_StartMeasurement(void)
{
    s_sig_gen.work_mode = MODE_MEASURE;
    s_sig_gen.display_update_needed = true;
    
    // 这里可以添加实际的测量逻辑
    // 暂时使用模拟数据
    s_measurement.input_freq_hz = s_sig_gen.frequency_hz;
    s_measurement.input_amp_mv = s_sig_gen.amplitude_mv;
    s_measurement.phase_deg = 45.0f;
    s_measurement.gain_db = -3.0f;
    s_measurement.valid = true;
    
    return true;
}

/**
  * @brief  获取测量结果
  * @param  None
  * @retval 测量结果结构体指针
  */
const Measurement_Result_t* SignalGenerator_GetMeasurement(void)
{
    return &s_measurement;
}

/**
  * @brief  强制更新显示
  * @param  None
  * @retval None
  */
void SignalGenerator_UpdateDisplay(void)
{
    if (!g_signal_generator_initialized) {
        return;
    }
    
    OLED_Clear();
    
    switch (s_sig_gen.work_mode) {
        case MODE_NORMAL:
            SignalGenerator_DisplayMain();
            break;
        case MODE_FREQ_SET:
            SignalGenerator_DisplayFreqSet();
            break;
        case MODE_AMP_SET:
            SignalGenerator_DisplayAmpSet();
            break;
        case MODE_WAVE_SEL:
            SignalGenerator_DisplayWaveSet();
            break;
        case MODE_MEASURE:
            SignalGenerator_DisplayMeasurement();
            break;
        default:
            SignalGenerator_DisplayMain();
            break;
    }
    
    OLED_Refresh_Gram();
}

/**
  * @brief  检查是否就绪
  * @param  None
  * @retval true: 就绪, false: 未就绪
  */
bool SignalGenerator_IsReady(void)
{
    return g_signal_generator_initialized;
}

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  处理第一块键盘输入 (数字输入键盘)
  * @param  key_code: 按键编码
  * @retval None
  */
static void SignalGenerator_ProcessKeypad1(uint8_t key_code)
{
    switch (key_code) {
        case KEY1_1: case KEY1_2: case KEY1_3:
        case KEY1_4: case KEY1_5: case KEY1_6:
        case KEY1_7: case KEY1_8: case KEY1_9:
        case KEY1_0:
            // 数字输入
            if (s_sig_gen.edit_state == EDIT_STATE_INPUT) {
                char digit = (key_code == KEY1_0) ? '0' : ('0' + key_code);
                SignalGenerator_ProcessDigitInput(digit);
            }
            break;

        case KEY1_STAR:
            // 清除当前输入
            SignalGenerator_ClearInputBuffer();
            break;

        case KEY1_HASH:
            // 确认输入
            if (s_sig_gen.edit_state == EDIT_STATE_INPUT) {
                if (s_sig_gen.work_mode == MODE_FREQ_SET) {
                    uint32_t freq = SignalGenerator_ParseFrequency(s_sig_gen.freq_buffer);
                    if (SIG_GEN_IS_FREQ_VALID(freq)) {
                        SignalGenerator_SetFrequency(freq);
                        s_sig_gen.work_mode = MODE_NORMAL;
                        s_sig_gen.edit_state = EDIT_STATE_NONE;
                    }
                } else if (s_sig_gen.work_mode == MODE_AMP_SET) {
                    uint16_t amp = SignalGenerator_ParseAmplitude(s_sig_gen.amp_buffer);
                    if (SIG_GEN_IS_AMP_VALID(amp)) {
                        SignalGenerator_SetAmplitude(amp);
                        s_sig_gen.work_mode = MODE_NORMAL;
                        s_sig_gen.edit_state = EDIT_STATE_NONE;
                    }
                }
            }
            break;

        case KEY1_FREQ_UP:
            // 频率增加
            if (s_sig_gen.frequency_hz + SIG_GEN_FREQ_STEP_HZ <= SIG_GEN_FREQ_MAX_HZ) {
                SignalGenerator_SetFrequency(s_sig_gen.frequency_hz + SIG_GEN_FREQ_STEP_HZ);
            }
            break;

        case KEY1_FREQ_DOWN:
            // 频率减少
            if (s_sig_gen.frequency_hz >= SIG_GEN_FREQ_MIN_HZ + SIG_GEN_FREQ_STEP_HZ) {
                SignalGenerator_SetFrequency(s_sig_gen.frequency_hz - SIG_GEN_FREQ_STEP_HZ);
            }
            break;

        case KEY1_AMP_UP:
            // 幅度增加
            if (s_sig_gen.amplitude_mv + SIG_GEN_AMP_STEP_MV <= SIG_GEN_AMP_MAX_MV) {
                SignalGenerator_SetAmplitude(s_sig_gen.amplitude_mv + SIG_GEN_AMP_STEP_MV);
            }
            break;

        case KEY1_AMP_DOWN:
            // 幅度减少
            if (s_sig_gen.amplitude_mv >= SIG_GEN_AMP_MIN_MV + SIG_GEN_AMP_STEP_MV) {
                SignalGenerator_SetAmplitude(s_sig_gen.amplitude_mv - SIG_GEN_AMP_STEP_MV);
            }
            break;

        default:
            break;
    }
}

/**
  * @brief  处理第二块键盘输入 (功能控制键盘)
  * @param  key_code: 按键编码
  * @retval None
  */
static void SignalGenerator_ProcessKeypad2(uint8_t key_code)
{
    switch (key_code) {
        case KEY2_FREQ_SET:
            // 进入频率设置模式
            s_sig_gen.work_mode = MODE_FREQ_SET;
            s_sig_gen.edit_state = EDIT_STATE_INPUT;
            SignalGenerator_ClearInputBuffer();
            sprintf(s_sig_gen.freq_buffer, "%lu", s_sig_gen.frequency_hz);
            s_sig_gen.buffer_index = strlen(s_sig_gen.freq_buffer);
            break;

        case KEY2_AMP_SET:
            // 进入幅度设置模式
            s_sig_gen.work_mode = MODE_AMP_SET;
            s_sig_gen.edit_state = EDIT_STATE_INPUT;
            SignalGenerator_ClearInputBuffer();
            sprintf(s_sig_gen.amp_buffer, "%u", s_sig_gen.amplitude_mv);
            s_sig_gen.buffer_index = strlen(s_sig_gen.amp_buffer);
            break;

        case KEY2_WAVE_SEL:
            // 进入波形选择模式
            s_sig_gen.work_mode = MODE_WAVE_SEL;
            break;

        case KEY2_MEASURE:
            // 进入测量模式
            SignalGenerator_StartMeasurement();
            break;

        case KEY2_SIN:
            SignalGenerator_SetWaveType(WAVE_SINE);
            break;

        case KEY2_SQUARE:
            SignalGenerator_SetWaveType(WAVE_SQUARE);
            break;

        case KEY2_TRIANGLE:
            SignalGenerator_SetWaveType(WAVE_TRIANGLE);
            break;

        case KEY2_SAWTOOTH:
            SignalGenerator_SetWaveType(WAVE_SAWTOOTH);
            break;

        case KEY2_ENTER:
            // 确认当前操作
            if (s_sig_gen.work_mode != MODE_NORMAL) {
                s_sig_gen.work_mode = MODE_NORMAL;
                s_sig_gen.edit_state = EDIT_STATE_NONE;
            }
            break;

        case KEY2_ESCAPE:
            // 退出当前模式
            s_sig_gen.work_mode = MODE_NORMAL;
            s_sig_gen.edit_state = EDIT_STATE_NONE;
            break;

        case KEY2_CLEAR:
            // 清除输入
            SignalGenerator_ClearInputBuffer();
            break;

        default:
            break;
    }

    s_sig_gen.display_update_needed = true;
}

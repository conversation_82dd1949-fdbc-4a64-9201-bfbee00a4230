/**
  ******************************************************************************
  * @file    signal_generator.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024
  * @brief   信号发生器控制模块实现
  ******************************************************************************
  */

#include "signal_generator.h"
#include "ad9910_control.h"
#include "../Core/systick.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

/* Private typedef -----------------------------------------------------------*/

/* Private define ------------------------------------------------------------*/

/* Private macro -------------------------------------------------------------*/

/* Private variables ---------------------------------------------------------*/

/**
  * @brief  初始化标志
  */
volatile bool g_signal_generator_initialized = false;

/**
  * @brief  信号发生器参数
  */
static Signal_Generator_t s_sig_gen = {
    .frequency_hz = SIG_GEN_FREQ_DEFAULT_HZ,
    .amplitude_mv = SIG_GEN_AMP_DEFAULT_MV,
    .wave_type = WAVE_SINE,
    .work_mode = MODE_NORMAL,
    .edit_state = EDIT_STATE_NONE,
    .edit_position = 0,
    .freq_buffer = {0},
    .amp_buffer = {0},
    .buffer_index = 0,
    .parameter_changed = true,
    .display_update_needed = true
};

/**
  * @brief  测量结果
  */
static Measurement_Result_t s_measurement = {
    .input_freq_hz = 0,
    .input_amp_mv = 0,
    .phase_deg = 0.0f,
    .gain_db = 0.0f,
    .valid = false
};

/**
  * @brief  波形名称表
  */
static const char* s_wave_names[WAVE_COUNT] = {
    "SIN", "SQR", "TRI", "SAW"
};

/**
  * @brief  模式名称表 (预留用于调试)
  */
// static const char* s_mode_names[MODE_COUNT] = {
//     "NORMAL", "FREQ", "AMP", "WAVE", "MEASURE"
// };

/**
  * @brief  上次显示更新时间 (已移除时间限制，保留用于扩展)
  */
// static uint32_t s_last_display_update = 0;

/**
  * @brief  上次按键扫描时间 (已移除时间限制，保留用于扩展)
  */
// static uint32_t s_last_key_scan = 0;

/**
  * @brief  上次按键时间 (防重复触发)
  */
static uint32_t s_last_key_time = 0;

/**
  * @brief  上次按键编码 (防重复触发)
  */
static uint8_t s_last_key_code = 0;
static Keypad_ID_t s_last_keypad_id = KEYPAD_1;

/**
  * @brief  显示缓存 - 用于差分更新
  */
static struct {
    uint32_t last_frequency;
    uint16_t last_amplitude;
    Wave_Type_t last_wave_type;
    Work_Mode_t last_work_mode;
    bool frequency_changed;
    bool amplitude_changed;
    bool wave_changed;
    bool mode_changed;
    bool force_full_update;
} s_display_cache = {0};

/* Private function prototypes -----------------------------------------------*/

static void SignalGenerator_ProcessKeypad1(uint8_t key_code);
static void SignalGenerator_ProcessKeypad2(uint8_t key_code);
static void SignalGenerator_UpdateParameters(void);
static void SignalGenerator_DisplayMain(void);
static void SignalGenerator_DisplayFreqSet(void);
static void SignalGenerator_DisplayAmpSet(void);
static void SignalGenerator_DisplayWaveSet(void);
static void SignalGenerator_DisplayMeasurement(void);
static void SignalGenerator_UpdateDisplayDiff(void);
static bool SignalGenerator_ProcessDigitInput(char digit);
static void SignalGenerator_ClearInputBuffer(void);
static uint32_t SignalGenerator_ParseFrequency(const char* buffer);
static uint16_t SignalGenerator_ParseAmplitude(const char* buffer);

/* Exported functions --------------------------------------------------------*/

/**
  * @brief  信号发生器初始化
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t SignalGenerator_Init(void)
{
    if (g_signal_generator_initialized) {
        return 0;
    }
    
    // 初始化矩阵键盘
    if (MatrixKeypad_Init() != 0) {
        return -1;
    }
    
    // 初始化OLED显示
    // OLED_Init(); // 假设已经在main中初始化
    
    // 初始化AD9910控制
    if (AD9910_Control_Init() != 0) {
        return -1;
    }
    
    // 设置初始参数
    SignalGenerator_UpdateParameters();

    // 初始化显示缓存
    s_display_cache.force_full_update = true;
    s_display_cache.last_frequency = 0;
    s_display_cache.last_amplitude = 0;
    s_display_cache.last_wave_type = WAVE_COUNT; // 无效值，强制更新
    s_display_cache.last_work_mode = MODE_COUNT; // 无效值，强制更新

    g_signal_generator_initialized = true;

    // 强制立即显示初始界面
    s_sig_gen.display_update_needed = true;
    SignalGenerator_UpdateDisplay();

    return 0;
}

/**
  * @brief  信号发生器反初始化
  * @param  None
  * @retval None
  */
void SignalGenerator_DeInit(void)
{
    if (!g_signal_generator_initialized) {
        return;
    }
    
    MatrixKeypad_DeInit();
    // AD9910_Control_DeInit(); // 如果有的话
    
    g_signal_generator_initialized = false;
}

/**
  * @brief  信号发生器主循环处理
  * @param  None
  * @retval None
  */
void SignalGenerator_Process(void)
{
    if (!g_signal_generator_initialized) {
        return;
    }

    uint32_t current_time = SysTick_GetTick();

    // 极简按键扫描 - 无任何限制和防重复
    Key_Event_t key_event;
    if (MatrixKeypad_ScanAll(&key_event)) {
        if (key_event.state == KEY_STATE_PRESSED) {
            // 立即处理按键，无任何防重复
            if (key_event.keypad_id == KEYPAD_1) {
                SignalGenerator_ProcessKeypad1(key_event.key_code);
            } else if (key_event.keypad_id == KEYPAD_2) {
                SignalGenerator_ProcessKeypad2(key_event.key_code);
            }
            // 立即更新显示
            SignalGenerator_UpdateDisplay();
        }
    }

    // 显示更新已移至按键处理中，立即响应
    // 这里不再需要检查display_update_needed，因为按键处理时就立即更新了

    // 极速参数更新 - 立即更新，无任何延时
    if (s_sig_gen.parameter_changed) {
        SignalGenerator_UpdateParameters();
        s_sig_gen.parameter_changed = false;
    }
}

/**
  * @brief  设置频率
  * @param  frequency_hz: 频率值 (Hz)
  * @retval true: 成功, false: 失败
  */
bool SignalGenerator_SetFrequency(uint32_t frequency_hz)
{
    if (!SIG_GEN_IS_FREQ_VALID(frequency_hz)) {
        return false;
    }

    // 只有频率真正改变时才更新
    if (s_sig_gen.frequency_hz != frequency_hz) {
        s_sig_gen.frequency_hz = frequency_hz;
        s_sig_gen.parameter_changed = true;
        s_sig_gen.display_update_needed = true;
        // 标记频率变化
        s_display_cache.frequency_changed = true;
    }

    return true;
}

/**
  * @brief  设置幅度
  * @param  amplitude_mv: 幅度值 (mV)
  * @retval true: 成功, false: 失败
  */
bool SignalGenerator_SetAmplitude(uint16_t amplitude_mv)
{
    if (!SIG_GEN_IS_AMP_VALID(amplitude_mv)) {
        return false;
    }

    // 只有幅度真正改变时才更新
    if (s_sig_gen.amplitude_mv != amplitude_mv) {
        s_sig_gen.amplitude_mv = amplitude_mv;
        s_sig_gen.parameter_changed = true;
        s_sig_gen.display_update_needed = true;
        // 标记幅度变化
        s_display_cache.amplitude_changed = true;
    }

    return true;
}

/**
  * @brief  设置波形类型
  * @param  wave_type: 波形类型
  * @retval true: 成功, false: 失败
  */
bool SignalGenerator_SetWaveType(Wave_Type_t wave_type)
{
    if (wave_type >= WAVE_COUNT) {
        return false;
    }

    // 只有波形真正改变时才更新
    if (s_sig_gen.wave_type != wave_type) {
        s_sig_gen.wave_type = wave_type;
        s_sig_gen.parameter_changed = true;
        s_sig_gen.display_update_needed = true;
        // 标记波形变化
        s_display_cache.wave_changed = true;
    }

    return true;
}

/**
  * @brief  获取当前参数
  * @param  None
  * @retval 信号发生器参数结构体指针
  */
const Signal_Generator_t* SignalGenerator_GetParams(void)
{
    return &s_sig_gen;
}

/**
  * @brief  开始测量模式
  * @param  None
  * @retval true: 成功, false: 失败
  */
bool SignalGenerator_StartMeasurement(void)
{
    s_sig_gen.work_mode = MODE_MEASURE;
    s_sig_gen.display_update_needed = true;
    
    // 这里可以添加实际的测量逻辑
    // 暂时使用模拟数据
    s_measurement.input_freq_hz = s_sig_gen.frequency_hz;
    s_measurement.input_amp_mv = s_sig_gen.amplitude_mv;
    s_measurement.phase_deg = 45.0f;
    s_measurement.gain_db = -3.0f;
    s_measurement.valid = true;
    
    return true;
}

/**
  * @brief  获取测量结果
  * @param  None
  * @retval 测量结果结构体指针
  */
const Measurement_Result_t* SignalGenerator_GetMeasurement(void)
{
    return &s_measurement;
}

/**
  * @brief  强制更新显示
  * @param  None
  * @retval None
  */
void SignalGenerator_UpdateDisplay(void)
{
    if (!g_signal_generator_initialized) {
        return;
    }

    // 极速全屏更新 - 移除所有复杂的差分逻辑，直接全速刷新
    OLED_Clear();

    switch (s_sig_gen.work_mode) {
        case MODE_NORMAL:
            SignalGenerator_DisplayMain();
            break;
        case MODE_FREQ_SET:
            SignalGenerator_DisplayFreqSet();
            break;
        case MODE_AMP_SET:
            SignalGenerator_DisplayAmpSet();
            break;
        case MODE_WAVE_SEL:
            SignalGenerator_DisplayWaveSet();
            break;
        case MODE_MEASURE:
            SignalGenerator_DisplayMeasurement();
            break;
        default:
            SignalGenerator_DisplayMain();
            break;
    }

    // 立即刷新，无任何延时
    OLED_Refresh_Gram();

    // 更新缓存
    s_display_cache.last_frequency = s_sig_gen.frequency_hz;
    s_display_cache.last_amplitude = s_sig_gen.amplitude_mv;
    s_display_cache.last_wave_type = s_sig_gen.wave_type;
    s_display_cache.last_work_mode = s_sig_gen.work_mode;
    s_display_cache.force_full_update = false;

    // 清除变化标志
    s_display_cache.frequency_changed = false;
    s_display_cache.amplitude_changed = false;
    s_display_cache.wave_changed = false;
    s_display_cache.mode_changed = false;
}

/**
  * @brief  检查是否就绪
  * @param  None
  * @retval true: 就绪, false: 未就绪
  */
bool SignalGenerator_IsReady(void)
{
    return g_signal_generator_initialized;
}

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  处理第一块键盘输入 (数字输入键盘)
  * @param  key_code: 按键编码
  * @retval None
  */
static void SignalGenerator_ProcessKeypad1(uint8_t key_code)
{
    switch (key_code) {
        case KEY1_1: case KEY1_2: case KEY1_3:
        case KEY1_4: case KEY1_5: case KEY1_6:
        case KEY1_7: case KEY1_8: case KEY1_9:
        case KEY1_0:
            // 数字输入
            if (s_sig_gen.edit_state == EDIT_STATE_INPUT) {
                char digit = (key_code == KEY1_0) ? '0' : ('0' + key_code);
                if (SignalGenerator_ProcessDigitInput(digit)) {
                    // 输入成功，立即更新显示
                    s_sig_gen.display_update_needed = true;
                    s_display_cache.force_full_update = true;
                }
            }
            break;

        case KEY1_STAR:
            // 清除当前输入
            SignalGenerator_ClearInputBuffer();
            // 立即更新显示
            s_sig_gen.display_update_needed = true;
            s_display_cache.force_full_update = true;
            break;

        case KEY1_HASH:
            // 全局确认键 - 立即应用当前参数并生成新波形
            if (s_sig_gen.edit_state == EDIT_STATE_INPUT) {
                // 如果在输入模式，确认输入
                if (s_sig_gen.work_mode == MODE_FREQ_SET) {
                    uint32_t freq = SignalGenerator_ParseFrequency(s_sig_gen.freq_buffer);
                    if (SIG_GEN_IS_FREQ_VALID(freq)) {
                        SignalGenerator_SetFrequency(freq);
                    }
                } else if (s_sig_gen.work_mode == MODE_AMP_SET) {
                    uint16_t amp = SignalGenerator_ParseAmplitude(s_sig_gen.amp_buffer);
                    if (SIG_GEN_IS_AMP_VALID(amp)) {
                        SignalGenerator_SetAmplitude(amp);
                    }
                }
            }

            // 无论什么模式，都立即应用参数并返回正常模式
            s_sig_gen.work_mode = MODE_NORMAL;
            s_sig_gen.edit_state = EDIT_STATE_NONE;

            // 强制立即更新AD9910参数 - 确保波形真正生成
            s_sig_gen.parameter_changed = true;
            SignalGenerator_UpdateParameters();

            // 再次强制更新，确保参数生效
            SignalGenerator_UpdateParameters();

            // 调用AD9910控制任务，确保参数真正写入硬件
            AD9910_Control_Task(); // 关键！确保波形真正生成

            // 强制全屏更新显示
            s_display_cache.force_full_update = true;
            s_sig_gen.display_update_needed = true;
            break;

        case KEY1_FREQ_UP:
            // 频率增加 - 极速响应
            if (s_sig_gen.frequency_hz <= SIG_GEN_FREQ_MAX_HZ - SIG_GEN_FREQ_STEP_HZ) {
                SignalGenerator_SetFrequency(s_sig_gen.frequency_hz + SIG_GEN_FREQ_STEP_HZ);
            } else {
                SignalGenerator_SetFrequency(SIG_GEN_FREQ_MAX_HZ);
            }
            // 立即更新显示和参数
            SignalGenerator_UpdateParameters();
            break;

        case KEY1_FREQ_DOWN:
            // 频率减少 - 极速响应
            if (s_sig_gen.frequency_hz > SIG_GEN_FREQ_STEP_HZ &&
                s_sig_gen.frequency_hz - SIG_GEN_FREQ_STEP_HZ >= SIG_GEN_FREQ_MIN_HZ) {
                SignalGenerator_SetFrequency(s_sig_gen.frequency_hz - SIG_GEN_FREQ_STEP_HZ);
            } else {
                // 如果减去步长会小于最小值，直接设为最小值
                SignalGenerator_SetFrequency(SIG_GEN_FREQ_MIN_HZ);
            }
            // 立即更新显示和参数
            SignalGenerator_UpdateParameters();
            break;

        case KEY1_AMP_UP:
            // 幅度增加 - 极速响应
            if (s_sig_gen.amplitude_mv <= SIG_GEN_AMP_MAX_MV - SIG_GEN_AMP_STEP_MV) {
                SignalGenerator_SetAmplitude(s_sig_gen.amplitude_mv + SIG_GEN_AMP_STEP_MV);
            } else {
                SignalGenerator_SetAmplitude(SIG_GEN_AMP_MAX_MV);
            }
            // 立即更新显示和参数
            SignalGenerator_UpdateParameters();
            break;

        case KEY1_AMP_DOWN:
            // 幅度减少 - 极速响应
            if (s_sig_gen.amplitude_mv > SIG_GEN_AMP_STEP_MV &&
                s_sig_gen.amplitude_mv - SIG_GEN_AMP_STEP_MV >= SIG_GEN_AMP_MIN_MV) {
                SignalGenerator_SetAmplitude(s_sig_gen.amplitude_mv - SIG_GEN_AMP_STEP_MV);
            } else {
                // 如果减去步长会小于最小值，直接设为最小值
                SignalGenerator_SetAmplitude(SIG_GEN_AMP_MIN_MV);
            }
            // 立即更新显示和参数
            SignalGenerator_UpdateParameters();
            break;

        default:
            break;
    }
}

/**
  * @brief  处理第二块键盘输入 (功能控制键盘)
  * @param  key_code: 按键编码
  * @retval None
  */
static void SignalGenerator_ProcessKeypad2(uint8_t key_code)
{
    switch (key_code) {
        case KEY2_FREQ_SET:
            // 进入频率设置模式
            s_sig_gen.work_mode = MODE_FREQ_SET;
            s_sig_gen.edit_state = EDIT_STATE_INPUT;
            SignalGenerator_ClearInputBuffer();
            // 强制全屏更新
            s_display_cache.force_full_update = true;
            break;

        case KEY2_AMP_SET:
            // 进入幅度设置模式
            s_sig_gen.work_mode = MODE_AMP_SET;
            s_sig_gen.edit_state = EDIT_STATE_INPUT;
            SignalGenerator_ClearInputBuffer();
            // 强制全屏更新
            s_display_cache.force_full_update = true;
            break;

        case KEY2_LEFT_SHIFT:
            // 左移位功能 (预留，当前返回正常模式)
            s_sig_gen.work_mode = MODE_NORMAL;
            s_sig_gen.edit_state = EDIT_STATE_NONE;
            break;

        case KEY2_RIGHT_SHIFT:
            // 右移位功能 (预留，当前返回正常模式)
            s_sig_gen.work_mode = MODE_NORMAL;
            s_sig_gen.edit_state = EDIT_STATE_NONE;
            break;

        case KEY2_SIN:
            // 输出正弦波 - 极速响应
            SignalGenerator_SetWaveType(WAVE_SINE);
            SignalGenerator_UpdateParameters();
            break;

        case KEY2_SQUARE:
            // 输出方波 - 极速响应
            SignalGenerator_SetWaveType(WAVE_SQUARE);
            SignalGenerator_UpdateParameters();
            break;

        case KEY2_TRIANGLE:
            // 输出三角波 - 极速响应
            SignalGenerator_SetWaveType(WAVE_TRIANGLE);
            SignalGenerator_UpdateParameters();
            break;

        case KEY2_SAWTOOTH:
            // 输出锯齿波 - 极速响应
            SignalGenerator_SetWaveType(WAVE_SAWTOOTH);
            SignalGenerator_UpdateParameters();
            break;

        // 预留按键暂不处理
        case KEY2_RESERVED_5:
        case KEY2_RESERVED_6:
        case KEY2_RESERVED_7:
        case KEY2_RESERVED_8:
        case KEY2_RESERVED_13:
        case KEY2_RESERVED_14:
        case KEY2_RESERVED_15:
        case KEY2_RESERVED_16:
        default:
            // 预留功能或无效按键
            break;
    }

    s_sig_gen.display_update_needed = true;
}

/**
  * @brief  更新AD9910参数
  * @param  None
  * @retval None
  */
static void SignalGenerator_UpdateParameters(void)
{
    // 根据波形类型设置AD9910
    switch (s_sig_gen.wave_type) {
        case WAVE_SINE:
            // 设置正弦波
            AD9910_Control_SetFrequency(s_sig_gen.frequency_hz);
            AD9910_Control_SetTargetAmplitude(s_sig_gen.amplitude_mv);
            break;

        case WAVE_SQUARE:
        case WAVE_TRIANGLE:
        case WAVE_SAWTOOTH:
            // 其他波形暂时使用正弦波代替
            AD9910_Control_SetFrequency(s_sig_gen.frequency_hz);
            AD9910_Control_SetTargetAmplitude(s_sig_gen.amplitude_mv);
            break;

        default:
            // 默认使用正弦波
            AD9910_Control_SetFrequency(s_sig_gen.frequency_hz);
            AD9910_Control_SetTargetAmplitude(s_sig_gen.amplitude_mv);
            break;
    }

    // 确保参数立即生效 - 调用AD9910的任务函数
    AD9910_Control_Task(); // 这是关键！确保参数真正写入AD9910硬件
}

/**
  * @brief  显示主界面
  * @param  None
  * @retval None
  */
static void SignalGenerator_DisplayMain(void)
{
    char buffer[32];

    // 显示标题
    OLED_ShowString(0, 0, (u8*)"SIGNAL GEN");

    // 显示频率
    if (s_sig_gen.frequency_hz >= 1000000) {
        sprintf(buffer, "F:%luMHz", s_sig_gen.frequency_hz / 1000000);
    } else if (s_sig_gen.frequency_hz >= 1000) {
        sprintf(buffer, "F:%lukHz", s_sig_gen.frequency_hz / 1000);
    } else {
        sprintf(buffer, "F:%luHz", s_sig_gen.frequency_hz);
    }
    OLED_ShowString(0, 2, (u8*)buffer);

    // 显示幅度
    sprintf(buffer, "A:%u.%uV", s_sig_gen.amplitude_mv / 1000,
            (s_sig_gen.amplitude_mv % 1000) / 100);
    OLED_ShowString(0, 4, (u8*)buffer);

    // 显示波形
    sprintf(buffer, "W:%s", s_wave_names[s_sig_gen.wave_type]);
    OLED_ShowString(0, 6, (u8*)buffer);
}

/**
  * @brief  显示频率设置界面
  * @param  None
  * @retval None
  */
static void SignalGenerator_DisplayFreqSet(void)
{
    char buffer[32];

    OLED_ShowString(0, 0, (u8*)"FREQ SET (Hz)");

    if (s_sig_gen.edit_state == EDIT_STATE_INPUT && strlen(s_sig_gen.freq_buffer) > 0) {
        // 显示正在输入的数字
        sprintf(buffer, "Input:%s_", s_sig_gen.freq_buffer);
        OLED_ShowString(0, 2, (u8*)buffer);

        // 显示当前值作为参考
        if (s_sig_gen.frequency_hz >= 1000000) {
            sprintf(buffer, "Now:%luMHz", s_sig_gen.frequency_hz / 1000000);
        } else if (s_sig_gen.frequency_hz >= 1000) {
            sprintf(buffer, "Now:%lukHz", s_sig_gen.frequency_hz / 1000);
        } else {
            sprintf(buffer, "Now:%luHz", s_sig_gen.frequency_hz);
        }
        OLED_ShowString(0, 4, (u8*)buffer);
    } else {
        // 显示当前频率值
        if (s_sig_gen.frequency_hz >= 1000000) {
            sprintf(buffer, "F:%luMHz", s_sig_gen.frequency_hz / 1000000);
        } else if (s_sig_gen.frequency_hz >= 1000) {
            sprintf(buffer, "F:%lukHz", s_sig_gen.frequency_hz / 1000);
        } else {
            sprintf(buffer, "F:%luHz", s_sig_gen.frequency_hz);
        }
        OLED_ShowString(0, 2, (u8*)buffer);

        OLED_ShowString(0, 4, (u8*)"Enter digits");
    }

    OLED_ShowString(0, 6, (u8*)"# OK  * CLR");
}

/**
  * @brief  显示幅度设置界面
  * @param  None
  * @retval None
  */
static void SignalGenerator_DisplayAmpSet(void)
{
    char buffer[32];

    OLED_ShowString(0, 0, (u8*)"AMP SET (mV)");

    if (s_sig_gen.edit_state == EDIT_STATE_INPUT && strlen(s_sig_gen.amp_buffer) > 0) {
        // 显示正在输入的数字
        sprintf(buffer, "Input:%smV_", s_sig_gen.amp_buffer);
        OLED_ShowString(0, 2, (u8*)buffer);

        // 显示当前值作为参考
        sprintf(buffer, "Now:%u.%uV", s_sig_gen.amplitude_mv / 1000,
                (s_sig_gen.amplitude_mv % 1000) / 100);
        OLED_ShowString(0, 4, (u8*)buffer);
    } else {
        // 显示当前幅度值
        sprintf(buffer, "A:%u.%uV(%umV)", s_sig_gen.amplitude_mv / 1000,
                (s_sig_gen.amplitude_mv % 1000) / 100, s_sig_gen.amplitude_mv);
        OLED_ShowString(0, 2, (u8*)buffer);

        OLED_ShowString(0, 4, (u8*)"Enter mV value");
    }

    OLED_ShowString(0, 6, (u8*)"# OK  * CLR");
}

/**
  * @brief  显示波形设置界面
  * @param  None
  * @retval None
  */
static void SignalGenerator_DisplayWaveSet(void)
{
    char buffer[32];

    OLED_ShowString(0, 0, (u8*)"WAVE SEL");

    sprintf(buffer, "W:%s", s_wave_names[s_sig_gen.wave_type]);
    OLED_ShowString(0, 2, (u8*)buffer);

    OLED_ShowString(0, 4, (u8*)"SIN SQR TRI");
    OLED_ShowString(0, 6, (u8*)"ENT EXIT");
}

/**
  * @brief  显示测量界面
  * @param  None
  * @retval None
  */
static void SignalGenerator_DisplayMeasurement(void)
{
    char buffer[32];

    OLED_ShowString(0, 0, (u8*)"MEASURE");

    if (s_measurement.valid) {
        sprintf(buffer, "F:%luHz", s_measurement.input_freq_hz);
        OLED_ShowString(0, 2, (u8*)buffer);

        sprintf(buffer, "G:%.1fdB", s_measurement.gain_db);
        OLED_ShowString(0, 4, (u8*)buffer);

        sprintf(buffer, "P:%.1f°", s_measurement.phase_deg);
        OLED_ShowString(0, 6, (u8*)buffer);
    } else {
        OLED_ShowString(0, 2, (u8*)"MEASURING...");
        OLED_ShowString(0, 6, (u8*)"ESC EXIT");
    }
}

/**
  * @brief  处理数字输入
  * @param  digit: 输入的数字字符
  * @retval true: 成功, false: 失败
  */
static bool SignalGenerator_ProcessDigitInput(char digit)
{
    if (digit < '0' || digit > '9') {
        return false;
    }

    if (s_sig_gen.work_mode == MODE_FREQ_SET) {
        if (s_sig_gen.buffer_index < sizeof(s_sig_gen.freq_buffer) - 1) {
            s_sig_gen.freq_buffer[s_sig_gen.buffer_index++] = digit;
            s_sig_gen.freq_buffer[s_sig_gen.buffer_index] = '\0';
            s_sig_gen.display_update_needed = true;
            return true;
        }
    } else if (s_sig_gen.work_mode == MODE_AMP_SET) {
        if (s_sig_gen.buffer_index < sizeof(s_sig_gen.amp_buffer) - 1) {
            s_sig_gen.amp_buffer[s_sig_gen.buffer_index++] = digit;
            s_sig_gen.amp_buffer[s_sig_gen.buffer_index] = '\0';
            s_sig_gen.display_update_needed = true;
            return true;
        }
    }

    return false;
}

/**
  * @brief  清除输入缓冲区
  * @param  None
  * @retval None
  */
static void SignalGenerator_ClearInputBuffer(void)
{
    memset(s_sig_gen.freq_buffer, 0, sizeof(s_sig_gen.freq_buffer));
    memset(s_sig_gen.amp_buffer, 0, sizeof(s_sig_gen.amp_buffer));
    s_sig_gen.buffer_index = 0;
    s_sig_gen.display_update_needed = true;
}

/**
  * @brief  解析频率字符串
  * @param  buffer: 频率字符串
  * @retval 频率值 (Hz)
  */
static uint32_t SignalGenerator_ParseFrequency(const char* buffer)
{
    if (buffer == NULL || strlen(buffer) == 0) {
        return s_sig_gen.frequency_hz; // 返回当前值
    }

    uint32_t freq = (uint32_t)atol(buffer);
    return freq;
}

/**
  * @brief  解析幅度字符串
  * @param  buffer: 幅度字符串
  * @retval 幅度值 (mV)
  */
static uint16_t SignalGenerator_ParseAmplitude(const char* buffer)
{
    if (buffer == NULL || strlen(buffer) == 0) {
        return s_sig_gen.amplitude_mv; // 返回当前值
    }

    uint16_t amp = (uint16_t)atoi(buffer);
    return amp;
}

/**
  * @brief  差分更新显示 - 只更新变化的部分
  * @param  None
  * @retval None
  */
static void SignalGenerator_UpdateDisplayDiff(void)
{
    char buffer[32];

    // 只在正常模式下进行差分更新
    if (s_sig_gen.work_mode != MODE_NORMAL) {
        return;
    }

    // 极速更新频率显示 (第2行)
    if (s_display_cache.frequency_changed) {
        // 直接覆盖显示，无清除操作
        if (s_sig_gen.frequency_hz >= 1000000) {
            sprintf(buffer, "F:%luMHz        ", s_sig_gen.frequency_hz / 1000000);
        } else if (s_sig_gen.frequency_hz >= 1000) {
            sprintf(buffer, "F:%lukHz        ", s_sig_gen.frequency_hz / 1000);
        } else {
            sprintf(buffer, "F:%luHz         ", s_sig_gen.frequency_hz);
        }
        OLED_ShowString(0, 2, (u8*)buffer);
        s_display_cache.last_frequency = s_sig_gen.frequency_hz;
        // 立即刷新这一行
        OLED_Refresh_Gram();
    }

    // 极速更新幅度显示 (第4行)
    if (s_display_cache.amplitude_changed) {
        // 直接覆盖显示，无清除操作
        sprintf(buffer, "A:%u.%uV        ", s_sig_gen.amplitude_mv / 1000,
                (s_sig_gen.amplitude_mv % 1000) / 100);
        OLED_ShowString(0, 4, (u8*)buffer);
        s_display_cache.last_amplitude = s_sig_gen.amplitude_mv;
        // 立即刷新这一行
        OLED_Refresh_Gram();
    }

    // 极速更新波形显示 (第6行)
    if (s_display_cache.wave_changed) {
        // 直接覆盖显示，无清除操作
        sprintf(buffer, "W:%s        ", s_wave_names[s_sig_gen.wave_type]);
        OLED_ShowString(0, 6, (u8*)buffer);
        s_display_cache.last_wave_type = s_sig_gen.wave_type;
        // 立即刷新这一行
        OLED_Refresh_Gram();
    }
}



# 嘉立创天空星STM32F407VGT6引脚分配表

## 📋 项目引脚使用总览

本文档详细列出了STM32F4发挥部分第一问项目中所有GPIO引脚的使用情况，确保没有引脚冲突。

## 🔌 当前引脚分配

### PA端口 (GPIOA)
| 引脚 | 功能 | 模块 | 说明 |
|------|------|------|------|
| PA0 | 未使用 | - | 可用 |
| PA1 | 未使用 | - | 可用 |
| PA2 | AD9910_SCK | AD9910 | SPI时钟 |
| PA3 | 未使用 | - | 可用 |
| PA4 | AD9910_DRC | AD9910 | 数据就绪控制 |
| PA5 | AD9910_SDIO | AD9910 | SPI数据线 |
| PA6 | AD9910_RST | AD9910 | 复位信号 |
| PA7 | 未使用 | - | 可用 |
| PA8 | 未使用 | - | 可用 |
| PA9 | 未使用 | - | 可用 |
| PA10 | 未使用 | - | 可用 |
| PA11 | 未使用 | - | 可用 |
| PA12 | 未使用 | - | 可用 |
| PA13 | SWDIO | 调试 | SWD调试接口 |
| PA14 | SWCLK | 调试 | SWD调试接口 |
| PA15 | 未使用 | - | 可用 |

### PB端口 (GPIOB)
| 引脚 | 功能 | 模块 | 说明 |
|------|------|------|------|
| PB0 | 未使用 | - | 可用 |
| PB1 | 未使用 | - | 可用 |
| PB2 | 未使用 | - | 可用 |
| PB3 | 未使用 | - | 可用 |
| PB4 | 未使用 | - | 可用 |
| PB5 | 未使用 | - | 可用 |
| PB6 | OLED_SCL | OLED | I2C时钟线 |
| PB7 | OLED_SDA | OLED | I2C数据线 |
| PB8 | 未使用 | - | 可用 |
| PB9 | 未使用 | - | 可用 |
| PB10 | AD9910_CSB | AD9910 | 片选信号 |
| PB11 | 未使用 | - | 可用 |
| PB12 | 未使用 | - | 可用 |
| PB13 | 未使用 | - | 可用 |
| PB14 | 未使用 | - | 可用 |
| PB15 | 未使用 | - | 可用 |

### PC端口 (GPIOC)
| 引脚 | 功能 | 模块 | 说明 |
|------|------|------|------|
| PC0 | 未使用 | - | 可用 |
| PC1 | AD9910_DPH | AD9910 | 数字相位控制 |
| PC2 | AD9910_DRO | AD9910 | 数据就绪输出 |
| PC3 | AD9910_IOUP | AD9910 | I/O更新 |
| PC4 | AD9910_PF0 | AD9910 | 配置文件0 |
| PC5 | AD9910_PF2 | AD9910 | 配置文件2 |
| PC6 | 未使用 | - | 可用 |
| PC7 | 未使用 | - | 可用 |
| PC8 | AD9910_OSK | AD9910 | 输出移位键控 |
| PC9 | 未使用 | - | 可用 |
| PC10 | AD9910_PF1 | AD9910 | 配置文件1 |
| PC11 | 未使用 | - | 可用 |
| PC12 | 未使用 | - | 可用 |
| PC13 | AD9910_PWR | AD9910 | 电源控制 |
| PC14 | 未使用 | - | 可用 |
| PC15 | 未使用 | - | 可用 |

### PD端口 (GPIOD)
| 引脚 | 功能 | 模块 | 说明 |
|------|------|------|------|
| PD0 | 未使用 | - | 可用 |
| PD1 | 未使用 | - | 可用 |
| PD2 | 未使用 | - | 可用 |
| PD3 | 未使用 | - | 可用 |
| PD4 | 未使用 | - | 可用 |
| PD5 | 未使用 | - | 可用 |
| PD6 | 未使用 | - | 可用 |
| PD7 | 未使用 | - | 可用 |
| PD8 | 未使用 | - | 可用 |
| PD9 | 未使用 | - | 可用 |
| PD10 | 未使用 | - | 可用 |
| PD11 | 未使用 | - | 可用 |
| PD12 | 未使用 | - | 可用 |
| PD13 | 未使用 | - | 可用 |
| PD14 | 未使用 | - | 可用 |
| PD15 | 未使用 | - | 可用 |

### PE端口 (GPIOE) - 双矩阵键盘
| 引脚 | 功能 | 模块 | 说明 |
|------|------|------|------|
| PE0 | KEYPAD1_ROW0 | 矩阵键盘1 | 第1行控制线 |
| PE1 | KEYPAD1_ROW1 | 矩阵键盘1 | 第2行控制线 |
| PE2 | KEYPAD1_ROW2 | 矩阵键盘1 | 第3行控制线 |
| PE3 | KEYPAD1_ROW3 | 矩阵键盘1 | 第4行控制线 |
| PE4 | KEYPAD1_COL0 | 矩阵键盘1 | 第1列检测线 |
| PE5 | KEYPAD1_COL1 | 矩阵键盘1 | 第2列检测线 |
| PE6 | KEYPAD1_COL2 | 矩阵键盘1 | 第3列检测线 |
| PE7 | KEYPAD1_COL3 | 矩阵键盘1 | 第4列检测线 |
| PE8 | KEYPAD2_ROW0 | 矩阵键盘2 | 第1行控制线 |
| PE9 | KEYPAD2_ROW1 | 矩阵键盘2 | 第2行控制线 |
| PE10 | KEYPAD2_ROW2 | 矩阵键盘2 | 第3行控制线 |
| PE11 | KEYPAD2_ROW3 | 矩阵键盘2 | 第4行控制线 |
| PE12 | KEYPAD2_COL0 | 矩阵键盘2 | 第1列检测线 |
| PE13 | KEYPAD2_COL1 | 矩阵键盘2 | 第2列检测线 |
| PE14 | KEYPAD2_COL2 | 矩阵键盘2 | 第3列检测线 |
| PE15 | KEYPAD2_COL3 | 矩阵键盘2 | 第4列检测线 |

### PF端口 (GPIOF) - 不存在
**注意**: 嘉立创天空星STM32F407VGT6没有PF端口

### PG端口 (GPIOG)
| 引脚 | 功能 | 模块 | 说明 |
|------|------|------|------|
| PG0-PG15 | 未使用 | - | 全部可用 |

### PH端口 (GPIOH)
| 引脚 | 功能 | 模块 | 说明 |
|------|------|------|------|
| PH0 | OSC_IN | 时钟 | 外部晶振输入 |
| PH1 | OSC_OUT | 时钟 | 外部晶振输出 |
| PH2-PH15 | 未使用 | - | 可用 |

## 🔍 引脚冲突检查

### ✅ 已解决的冲突
1. **原设计冲突**: 第一块矩阵键盘使用PC0-PC7，与AD9910的PC1,PC2,PC3,PC4,PC5冲突
2. **解决方案**: 将第一块矩阵键盘移至PE0-PE7
3. **原设计冲突**: 第二块矩阵键盘使用PD0-PD7，PD口预留给AD9910扩展
4. **解决方案**: 将第二块矩阵键盘移至PF0-PF7

### ✅ 当前无冲突
- AD9910使用: PA2,PA4,PA5,PA6,PB10,PC1,PC2,PC3,PC4,PC5,PC8,PC10,PC13
- OLED使用: PB6,PB7
- 矩阵键盘1使用: PE0-PE7
- 矩阵键盘2使用: PE8-PE15
- 调试接口: PA13,PA14
- 时钟: PH0,PH1

## 📊 资源使用统计

| 端口 | 已使用引脚数 | 总引脚数 | 使用率 |
|------|-------------|----------|--------|
| PA | 5 | 16 | 31.25% |
| PB | 3 | 16 | 18.75% |
| PC | 7 | 16 | 43.75% |
| PD | 0 | 16 | 0% |
| PE | 16 | 16 | 100% |
| PG | 0 | 16 | 0% |
| PH | 2 | 16 | 12.5% |

**总计**: 33/112 引脚已使用 (29.46%)
**注意**: 嘉立创天空星STM32F407VGT6没有PF端口，实际可用引脚为112个

## 🚀 可扩展资源

### 完全空闲的端口
- **PD端口**: 16个引脚全部可用，适合扩展功能
- **PG端口**: 16个引脚全部可用，适合扩展功能

### 部分空闲的端口
- **PA端口**: 11个引脚可用 (PA0,PA1,PA3,PA7-PA12,PA15)
- **PB端口**: 13个引脚可用 (PB0-PB5,PB8,PB9,PB11-PB15)
- **PC端口**: 9个引脚可用 (PC0,PC6,PC7,PC9,PC11,PC12,PC14,PC15)
- **PE端口**: 0个引脚可用 (全部被矩阵键盘占用)
- **PH端口**: 14个引脚可用 (PH2-PH15)

## ⚠️ 注意事项

1. **AD9910引脚不可更改**: 已经过硬件验证，不建议修改
2. **OLED引脚已验证**: PB6,PB7的I2C功能正常
3. **矩阵键盘引脚**: PE和PF端口支持5V容忍，适合键盘应用
4. **调试接口**: PA13,PA14必须保留用于程序下载和调试
5. **时钟引脚**: PH0,PH1连接外部晶振，不可占用

## 🔧 硬件连接建议

### 矩阵键盘连接
1. **使用排针连接**: 便于调试和更换
2. **添加去耦电容**: 在VCC和GND之间加100nF电容
3. **上拉电阻**: 列线已配置内部上拉，无需外部电阻
4. **防抖电容**: 可在按键两端并联10-100pF电容减少抖动

### 信号完整性
1. **短连接线**: 矩阵键盘连接线尽量短，减少干扰
2. **屏蔽**: 如有EMI问题，可使用屏蔽线
3. **接地**: 确保所有模块共地

**引脚分配已优化完成，无冲突，可以安全使用。**

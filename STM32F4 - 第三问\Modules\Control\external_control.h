/**
  ******************************************************************************
  * @file    external_control.h
  * <AUTHOR> 第三问 外部控制接口
  * @version V1.0
  * @date    2024
  * @brief   外部控制接口头文件 (预留扩展)
  ******************************************************************************
  * @attention
  * 
  * 本文件为外部控制接口预留头文件
  * 可根据实际需求添加各种外部控制功能的定义和声明
  * 
  ******************************************************************************
  */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __EXTERNAL_CONTROL_H
#define __EXTERNAL_CONTROL_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include "../Generation/ad9854.h"

/* Exported types ------------------------------------------------------------*/

/* Exported constants --------------------------------------------------------*/

// 外部控制接口预留定义

/* Exported variables --------------------------------------------------------*/

/* Exported macro ------------------------------------------------------------*/

/* Exported functions --------------------------------------------------------*/

// ==================== 外部控制函数 ====================

/**
 * @brief  外部控制主循环处理
 * @param  None
 * @retval None
 */
void ExternalControl_Process(void);

#ifdef __cplusplus
}
#endif

#endif /* __EXTERNAL_CONTROL_H */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/

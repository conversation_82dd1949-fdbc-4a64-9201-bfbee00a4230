/**
  ******************************************************************************
  * @file    matrix_keypad.h
  * <AUTHOR>
  * @version V1.0
  * @date    2024
  * @brief   双4x4矩阵键盘驱动模块
  *          支持两个独立的4x4矩阵键盘，低电平导通
  ******************************************************************************
  * @attention
  * 
  * 硬件连接：
  *
  * 第一块4x4矩阵键盘 (数字输入键盘):
  * - 行线: PE0, PE1, PE2, PE3 (输出，推挽)
  * - 列线: PE4, PE5, PE6, PE7 (输入，上拉)
  *
  * 第二块4x4矩阵键盘 (功能控制键盘):
  * - 行线: PE8, PE9, PE10, PE11 (输出，推挽)
  * - 列线: PE12, PE13, PE14, PE15 (输入，上拉)
  * 
  * 键盘布局：
  * 
  * 第一块键盘 (数字输入):
  * +-----+-----+-----+-----+
  * |  1  |  2  |  3  | F+  |  F+ = 频率加(100Hz)
  * +-----+-----+-----+-----+
  * |  4  |  5  |  6  | F-  |  F- = 频率减(100Hz)
  * +-----+-----+-----+-----+
  * |  7  |  8  |  9  | A+  |  A+ = 幅度加(0.1V)
  * +-----+-----+-----+-----+
  * |  *  |  0  |  #  | A-  |  A- = 幅度减(0.1V)
  * +-----+-----+-----+-----+
  * 
  * 第二块键盘 (功能控制):
  * +-----+-----+-----+-----+
  * |FREQ | AMP |WAVE | MSR |  FREQ=频率设置, AMP=幅度设置
  * +-----+-----+-----+-----+  WAVE=波形选择, MSR=测量模式
  * | <<  | >>  | UP  | DN  |  <<=左移位, >>=右移位
  * +-----+-----+-----+-----+  UP=数值增, DN=数值减
  * | SIN | SQR | TRI | SAW |  波形选择: 正弦/方波/三角/锯齿
  * +-----+-----+-----+-----+
  * | SET | CLR | ENT | ESC |  SET=设置, CLR=清除, ENT=确认, ESC=退出
  * +-----+-----+-----+-----+
  *
  ******************************************************************************
  */

#ifndef __MATRIX_KEYPAD_H
#define __MATRIX_KEYPAD_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include <stdint.h>
#include <stdbool.h>

/* Exported types ------------------------------------------------------------*/

/**
  * @brief  键盘编号枚举
  */
typedef enum {
    KEYPAD_1 = 0,                   ///< 第一块键盘(数字输入)
    KEYPAD_2 = 1,                   ///< 第二块键盘(功能控制)
    KEYPAD_COUNT = 2                ///< 键盘总数
} Keypad_ID_t;

/**
  * @brief  第一块键盘按键编号 (数字输入键盘)
  */
typedef enum {
    KEY1_NONE = 0,
    // 第一行
    KEY1_1 = 1, KEY1_2 = 2, KEY1_3 = 3, KEY1_FREQ_UP = 4,
    // 第二行  
    KEY1_4 = 5, KEY1_5 = 6, KEY1_6 = 7, KEY1_FREQ_DOWN = 8,
    // 第三行
    KEY1_7 = 9, KEY1_8 = 10, KEY1_9 = 11, KEY1_AMP_UP = 12,
    // 第四行
    KEY1_STAR = 13, KEY1_0 = 14, KEY1_HASH = 15, KEY1_AMP_DOWN = 16
} Keypad1_Key_t;

/**
  * @brief  第二块键盘按键编号 (功能控制键盘)
  */
typedef enum {
    KEY2_NONE = 0,
    // 第一行 - 设置功能
    KEY2_FREQ_SET = 1, KEY2_AMP_SET = 2, KEY2_UNIT_SWITCH = 3, KEY2_CONFIRM = 4,
    // 第二行 - 单位选择
    KEY2_HZ = 5, KEY2_KHZ = 6, KEY2_MHZ = 7, KEY2_VOLT = 8,
    // 第三行 - 波形选择
    KEY2_SIN = 9, KEY2_SQUARE = 10, KEY2_TRIANGLE = 11, KEY2_SAWTOOTH = 12,
    // 第四行 - 控制功能
    KEY2_CLEAR = 13, KEY2_BACKSPACE = 14, KEY2_ENTER = 15, KEY2_ESCAPE = 16
} Keypad2_Key_t;

/**
  * @brief  按键状态枚举
  */
typedef enum {
    KEY_STATE_RELEASED = 0,         ///< 按键释放
    KEY_STATE_PRESSED = 1,          ///< 按键按下
    KEY_STATE_DEBOUNCE = 2          ///< 消抖中
} Key_State_t;

/**
  * @brief  按键事件结构体
  */
typedef struct {
    Keypad_ID_t keypad_id;          ///< 键盘编号
    uint8_t key_code;               ///< 按键编码
    Key_State_t state;              ///< 按键状态
    uint32_t timestamp;             ///< 时间戳
} Key_Event_t;

/* Exported constants --------------------------------------------------------*/

/** @defgroup MATRIX_KEYPAD_Exported_Constants 矩阵键盘导出常量
  * @{
  */

#define KEYPAD_DEBOUNCE_TIME_MS     20U      ///< 消抖时间(ms)
#define KEYPAD_SCAN_INTERVAL_MS     10U      ///< 扫描间隔(ms)
#define KEYPAD_ROWS                 4U       ///< 键盘行数
#define KEYPAD_COLS                 4U       ///< 键盘列数

/**
  * @}
  */

/* Exported macro ------------------------------------------------------------*/

/** @defgroup MATRIX_KEYPAD_Exported_Macros 矩阵键盘导出宏
  * @{
  */

// 第一块键盘GPIO定义
#define KEYPAD1_ROW_PORT            GPIOE
#define KEYPAD1_ROW_PINS            (GPIO_Pin_0 | GPIO_Pin_1 | GPIO_Pin_2 | GPIO_Pin_3)
#define KEYPAD1_COL_PORT            GPIOE
#define KEYPAD1_COL_PINS            (GPIO_Pin_4 | GPIO_Pin_5 | GPIO_Pin_6 | GPIO_Pin_7)

// 第二块键盘GPIO定义
#define KEYPAD2_ROW_PORT            GPIOE
#define KEYPAD2_ROW_PINS            (GPIO_Pin_8 | GPIO_Pin_9 | GPIO_Pin_10 | GPIO_Pin_11)
#define KEYPAD2_COL_PORT            GPIOE
#define KEYPAD2_COL_PINS            (GPIO_Pin_12 | GPIO_Pin_13 | GPIO_Pin_14 | GPIO_Pin_15)

/**
  * @}
  */

/* Exported variables --------------------------------------------------------*/

/** @defgroup MATRIX_KEYPAD_Exported_Variables 矩阵键盘导出变量
  * @{
  */

extern volatile bool g_keypad_initialized;   ///< 键盘初始化标志

/**
  * @}
  */

/* Exported functions --------------------------------------------------------*/

/** @defgroup MATRIX_KEYPAD_Exported_Functions 矩阵键盘导出函数
  * @{
  */

/**
  * @brief  矩阵键盘初始化
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t MatrixKeypad_Init(void);

/**
  * @brief  矩阵键盘反初始化
  * @param  None
  * @retval None
  */
void MatrixKeypad_DeInit(void);

/**
  * @brief  扫描指定键盘
  * @param  keypad_id: 键盘编号
  * @retval 按键编码 (0表示无按键)
  */
uint8_t MatrixKeypad_Scan(Keypad_ID_t keypad_id);

/**
  * @brief  扫描所有键盘
  * @param  event: 按键事件结构体指针
  * @retval true: 有按键事件, false: 无按键事件
  */
bool MatrixKeypad_ScanAll(Key_Event_t* event);

/**
  * @brief  获取按键名称字符串
  * @param  keypad_id: 键盘编号
  * @param  key_code: 按键编码
  * @retval 按键名称字符串
  */
const char* MatrixKeypad_GetKeyName(Keypad_ID_t keypad_id, uint8_t key_code);

/**
  * @brief  检查键盘是否就绪
  * @param  None
  * @retval true: 就绪, false: 未就绪
  */
bool MatrixKeypad_IsReady(void);

/**
  * @}
  */

#ifdef __cplusplus
}
#endif

#endif /* __MATRIX_KEYPAD_H */

/************************ (C) COPYRIGHT 嵌入式竞赛团队 *****END OF FILE****/

/**
  ******************************************************************************
  * @file    signal_generator.h
  * <AUTHOR>
  * @version V1.0
  * @date    2024
  * @brief   信号发生器控制模块
  *          集成矩阵键盘输入、OLED显示、AD9910控制
  ******************************************************************************
  * @attention
  * 
  * 功能特性：
  * - 双键盘输入处理
  * - 频率设置 (1Hz - 100MHz)
  * - 幅度设置 (0.1V - 5.0V)
  * - 波形选择 (正弦波、方波、三角波、锯齿波)
  * - 实时OLED显示
  * - 未知电路测量功能
  * - 参数数字编辑功能
  *
  ******************************************************************************
  */

#ifndef __SIGNAL_GENERATOR_H
#define __SIGNAL_GENERATOR_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include <stdint.h>
#include <stdbool.h>
#include "../Interface/matrix_keypad.h"
#include "../Interface/oled.h"

/* Exported types ------------------------------------------------------------*/

/**
  * @brief  波形类型枚举
  */
typedef enum {
    WAVE_SINE = 0,                  ///< 正弦波
    WAVE_SQUARE = 1,                ///< 方波
    WAVE_TRIANGLE = 2,              ///< 三角波
    WAVE_SAWTOOTH = 3,              ///< 锯齿波
    WAVE_COUNT = 4                  ///< 波形总数
} Wave_Type_t;

/**
  * @brief  工作模式枚举
  */
typedef enum {
    MODE_NORMAL = 0,                ///< 正常信号发生模式
    MODE_FREQ_SET = 1,              ///< 频率设置模式
    MODE_AMP_SET = 2,               ///< 幅度设置模式
    MODE_WAVE_SEL = 3,              ///< 波形选择模式
    MODE_MEASURE = 4,               ///< 测量模式
    MODE_COUNT = 5                  ///< 模式总数
} Work_Mode_t;

/**
  * @brief  数字编辑状态枚举
  */
typedef enum {
    EDIT_STATE_NONE = 0,            ///< 无编辑状态
    EDIT_STATE_INPUT = 1,           ///< 数字输入状态
    EDIT_STATE_POSITION = 2         ///< 位置选择状态
} Edit_State_t;

/**
  * @brief  信号发生器参数结构体
  */
typedef struct {
    uint32_t frequency_hz;          ///< 频率 (Hz)
    uint16_t amplitude_mv;          ///< 幅度 (mV)
    Wave_Type_t wave_type;          ///< 波形类型
    Work_Mode_t work_mode;          ///< 工作模式
    Edit_State_t edit_state;        ///< 编辑状态
    uint8_t edit_position;          ///< 编辑位置
    char freq_buffer[16];           ///< 频率编辑缓冲区
    char amp_buffer[8];             ///< 幅度编辑缓冲区
    uint8_t buffer_index;           ///< 缓冲区索引
    bool parameter_changed;         ///< 参数改变标志
    bool display_update_needed;     ///< 显示更新标志
} Signal_Generator_t;

/**
  * @brief  测量结果结构体
  */
typedef struct {
    uint32_t input_freq_hz;         ///< 输入频率
    uint16_t input_amp_mv;          ///< 输入幅度
    float phase_deg;                ///< 相位差
    float gain_db;                  ///< 增益
    bool valid;                     ///< 测量结果有效性
} Measurement_Result_t;

/* Exported constants --------------------------------------------------------*/

/** @defgroup SIGNAL_GENERATOR_Exported_Constants 信号发生器导出常量
  * @{
  */

#define SIG_GEN_FREQ_MIN_HZ         1U          ///< 最小频率 1Hz
#define SIG_GEN_FREQ_MAX_HZ         100000000U  ///< 最大频率 100MHz
#define SIG_GEN_FREQ_DEFAULT_HZ     1000U       ///< 默认频率 1kHz

#define SIG_GEN_AMP_MIN_MV          100U        ///< 最小幅度 0.1V
#define SIG_GEN_AMP_MAX_MV          5000U       ///< 最大幅度 5.0V
#define SIG_GEN_AMP_DEFAULT_MV      1000U       ///< 默认幅度 1.0V

#define SIG_GEN_FREQ_STEP_HZ        100U        ///< 频率步长 100Hz
#define SIG_GEN_AMP_STEP_MV         100U        ///< 幅度步长 0.1V

#define SIG_GEN_DISPLAY_UPDATE_MS   20U         ///< 显示更新间隔 (高频更新20ms)
#define SIG_GEN_KEY_SCAN_MS         2U          ///< 按键扫描间隔 (高频扫描2ms)
#define SIG_GEN_PARAM_UPDATE_MS     1U          ///< 参数更新间隔 (立即更新1ms)

/**
  * @}
  */

/* Exported macro ------------------------------------------------------------*/

/** @defgroup SIGNAL_GENERATOR_Exported_Macros 信号发生器导出宏
  * @{
  */

/**
  * @brief  频率范围检查
  */
#define SIG_GEN_IS_FREQ_VALID(freq) \
    ((freq) >= SIG_GEN_FREQ_MIN_HZ && (freq) <= SIG_GEN_FREQ_MAX_HZ)

/**
  * @brief  幅度范围检查
  */
#define SIG_GEN_IS_AMP_VALID(amp) \
    ((amp) >= SIG_GEN_AMP_MIN_MV && (amp) <= SIG_GEN_AMP_MAX_MV)

/**
  * @}
  */

/* Exported variables --------------------------------------------------------*/

/** @defgroup SIGNAL_GENERATOR_Exported_Variables 信号发生器导出变量
  * @{
  */

extern volatile bool g_signal_generator_initialized;    ///< 初始化标志

/**
  * @}
  */

/* Exported functions --------------------------------------------------------*/

/** @defgroup SIGNAL_GENERATOR_Exported_Functions 信号发生器导出函数
  * @{
  */

/**
  * @brief  信号发生器初始化
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t SignalGenerator_Init(void);

/**
  * @brief  信号发生器反初始化
  * @param  None
  * @retval None
  */
void SignalGenerator_DeInit(void);

/**
  * @brief  信号发生器主循环处理
  * @param  None
  * @retval None
  */
void SignalGenerator_Process(void);

/**
  * @brief  设置频率
  * @param  frequency_hz: 频率值 (Hz)
  * @retval true: 成功, false: 失败
  */
bool SignalGenerator_SetFrequency(uint32_t frequency_hz);

/**
  * @brief  设置幅度
  * @param  amplitude_mv: 幅度值 (mV)
  * @retval true: 成功, false: 失败
  */
bool SignalGenerator_SetAmplitude(uint16_t amplitude_mv);

/**
  * @brief  设置波形类型
  * @param  wave_type: 波形类型
  * @retval true: 成功, false: 失败
  */
bool SignalGenerator_SetWaveType(Wave_Type_t wave_type);

/**
  * @brief  获取当前参数
  * @param  None
  * @retval 信号发生器参数结构体指针
  */
const Signal_Generator_t* SignalGenerator_GetParams(void);

/**
  * @brief  开始测量模式
  * @param  None
  * @retval true: 成功, false: 失败
  */
bool SignalGenerator_StartMeasurement(void);

/**
  * @brief  获取测量结果
  * @param  None
  * @retval 测量结果结构体指针
  */
const Measurement_Result_t* SignalGenerator_GetMeasurement(void);

/**
  * @brief  强制更新显示
  * @param  None
  * @retval None
  */
void SignalGenerator_UpdateDisplay(void);

/**
  * @brief  检查是否就绪
  * @param  None
  * @retval true: 就绪, false: 未就绪
  */
bool SignalGenerator_IsReady(void);

/**
  * @}
  */

#ifdef __cplusplus
}
#endif

#endif /* __SIGNAL_GENERATOR_H */

/************************ (C) COPYRIGHT 嵌入式竞赛团队 *****END OF FILE****/

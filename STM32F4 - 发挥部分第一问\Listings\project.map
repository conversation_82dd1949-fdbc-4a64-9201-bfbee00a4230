Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    startup_stm32f40_41xxx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(.text) for Reset_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.EXTI0_IRQHandler) for EXTI0_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.TIM6_DAC_IRQHandler) for TIM6_DAC_IRQHandler
    startup_stm32f40_41xxx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f40_41xxx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(HEAP) for Heap_Mem
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(STACK) for Stack_Mem
    main.o(i.PrintGainCalculationDetails) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.PrintGainCalculationDetails) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(i.PrintGainCalculationDetails) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    main.o(i.PrintGainCalculationDetails) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    main.o(i.PrintGainCalculationDetails) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    main.o(i.PrintGainCalculationDetails) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(i.PrintGainCalculationDetails) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    main.o(i.PrintGainCalculationDetails) refers to _printf_str.o(.text) for _printf_str
    main.o(i.PrintGainCalculationDetails) refers to gain_calculator.o(i.GainCalculator_TransferFunctionGain) for GainCalculator_TransferFunctionGain
    main.o(i.PrintGainCalculationDetails) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    main.o(i.PrintGainCalculationDetails) refers to gain_calculator.o(i.GainCalculator_GetAD9910Output) for GainCalculator_GetAD9910Output
    main.o(i.PrintGainCalculationDetails) refers to gain_calculator.o(i.GainCalculator_GetFinalOutput) for GainCalculator_GetFinalOutput
    main.o(i.PrintGainCalculationDetails) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    main.o(i.PrintGainCalculationDetails) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    main.o(i.PrintGainCalculationDetails) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    main.o(i.PrintGainCalculationDetails) refers to __2sprintf.o(.text) for __2sprintf
    main.o(i.PrintGainCalculationDetails) refers to main.o(.conststring) for .conststring
    main.o(i.PrintGainCalculationDetails) refers to main.o(.bss) for debug_info
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_DeInit) for RCC_DeInit
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_HSEConfig) for RCC_HSEConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp) for RCC_WaitForHSEStartUp
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_PLLConfig) for RCC_PLLConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_PLLCmd) for RCC_PLLCmd
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    main.o(i.SystemClock_Config) refers to stm32f4xx_flash.o(i.FLASH_SetLatency) for FLASH_SetLatency
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_HCLKConfig) for RCC_HCLKConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_PCLK1Config) for RCC_PCLK1Config
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_PCLK2Config) for RCC_PCLK2Config
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_SYSCLKConfig) for RCC_SYSCLKConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_GetSYSCLKSource) for RCC_GetSYSCLKSource
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to systick.o(i.SysTick_Init) for SysTick_Init
    main.o(i.main) refers to bsp.o(i.BSP_Init) for BSP_Init
    main.o(i.main) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to signal_generator.o(i.SignalGenerator_Init) for SignalGenerator_Init
    main.o(i.main) refers to oled.o(i.OLED_Clear) for OLED_Clear
    main.o(i.main) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.main) refers to oled.o(i.OLED_Refresh_Gram) for OLED_Refresh_Gram
    main.o(i.main) refers to command_parser.o(i.CommandParser_Init) for CommandParser_Init
    main.o(i.main) refers to gain_calculator.o(i.GainCalculator_Init) for GainCalculator_Init
    main.o(i.main) refers to ad9910_control.o(i.AD9910_Control_RegisterCallback) for AD9910_Control_RegisterCallback
    main.o(i.main) refers to gain_calculator.o(i.GainCalculator_TransferFunctionGain) for GainCalculator_TransferFunctionGain
    main.o(i.main) refers to ad9910_control.o(i.AD9910_Control_SetFrequency) for AD9910_Control_SetFrequency
    main.o(i.main) refers to ad9910_control.o(i.AD9910_Control_SetTargetAmplitude) for AD9910_Control_SetTargetAmplitude
    main.o(i.main) refers to ad9910_control.o(i.AD9910_Control_SetGainFactor) for AD9910_Control_SetGainFactor
    main.o(i.main) refers to ad9910_control.o(i.AD9910_Control_EnableOutput) for AD9910_Control_EnableOutput
    main.o(i.main) refers to main.o(i.PrintGainCalculationDetails) for PrintGainCalculationDetails
    main.o(i.main) refers to systick.o(i.Delay_ms) for Delay_ms
    main.o(i.main) refers to signal_generator.o(i.SignalGenerator_Process) for SignalGenerator_Process
    main.o(i.main) refers to ad9910_control.o(i.AD9910_Control_Task) for AD9910_Control_Task
    main.o(i.main) refers to main.o(i.ControlCallback) for ControlCallback
    stm32f4xx_it.o(i.EXTI0_IRQHandler) refers to main.o(i.EXTI0_IRQHandler_Internal) for EXTI0_IRQHandler_Internal
    stm32f4xx_it.o(i.SysTick_Handler) refers to main.o(i.TimingDelay_Decrement) for TimingDelay_Decrement
    stm32f4xx_it.o(i.SysTick_Handler) refers to systick.o(i.SysTick_Handler_Internal) for SysTick_Handler_Internal
    stm32f4xx_it.o(i.SysTick_Handler) refers to main.o(.data) for uwTick
    stm32f4xx_it.o(i.TIM2_IRQHandler) refers to stm32f4xx_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    stm32f4xx_it.o(i.TIM2_IRQHandler) refers to stm32f4xx_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    system_stm32f4xx.o(i.SystemInit) refers to system_stm32f4xx.o(i.SetSysClock) for SetSysClock
    bsp.o(i.BSP_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    bsp.o(i.BSP_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    misc.o(i.NVIC_Init) refers to main.o(i.assert_failed) for assert_failed
    misc.o(i.NVIC_PriorityGroupConfig) refers to main.o(i.assert_failed) for assert_failed
    misc.o(i.NVIC_SetVectorTable) refers to main.o(i.assert_failed) for assert_failed
    misc.o(i.NVIC_SystemLPConfig) refers to main.o(i.assert_failed) for assert_failed
    misc.o(i.SysTick_CLKSourceConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_AnalogWatchdogCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_AnalogWatchdogThresholdsConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_AutoInjectedConvCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_CommonInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_ContinuousModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_adc.o(i.ADC_DiscModeChannelCountConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_DiscModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_EOCOnEachRegularChannelCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvEdgeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_GetConversionValue) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_GetInjectedConversionValue) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_GetSoftwareStartConvStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_InjectedChannelConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_InjectedDiscModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_InjectedSequencerLengthConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_MultiModeDMARequestAfterLastTransferCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_RegularChannelConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_SetInjectedOffset) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_SoftwareStartConv) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_SoftwareStartInjectedConv) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_TempSensorVrefintCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_VBATCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_CancelTransmit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_DBGFreeze) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_can.o(i.CAN_FIFORelease) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_FilterInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_GetITStatus) refers to stm32f4xx_can.o(i.CheckITStatus) for CheckITStatus
    stm32f4xx_can.o(i.CAN_GetLSBTransmitErrorCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_GetLastErrorCode) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_GetReceiveErrorCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_MessagePending) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_OperatingModeRequest) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_Receive) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_SlaveStartBank) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_Sleep) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_TTComModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_Transmit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_TransmitStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_WakeUp) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp.o(i.CRYP_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp.o(i.CRYP_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp.o(i.CRYP_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd) for RCC_AHB2PeriphResetCmd
    stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp.o(i.CRYP_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp.o(i.CRYP_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp.o(i.CRYP_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp.o(i.CRYP_PhaseConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_PhaseConfig) for CRYP_PhaseConfig
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_PhaseConfig) for CRYP_PhaseConfig
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_dac.o(i.DAC_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_dac.o(i.DAC_DualSoftwareTriggerCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_GetDataOutputValue) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_SetChannel1Data) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_SetChannel2Data) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_SetDualChannelData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_SoftwareTriggerCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_WaveGenerationCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dbgmcu.o(i.DBGMCU_APB1PeriphConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dbgmcu.o(i.DBGMCU_APB2PeriphConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dbgmcu.o(i.DBGMCU_Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_CROPCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_CaptureCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_JPEGCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_DoubleBufferModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_DoubleBufferModeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_FlowControllerConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_GetCmdStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_GetCurrDataCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_GetCurrentMemoryTarget) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_GetFIFOStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_MemoryTargetConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_PeriphIncOffsetSizeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_SetCurrDataCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_BGConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_BGStart) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd) for RCC_AHB1PeriphResetCmd
    stm32f4xx_dma2d.o(i.DMA2D_DeadTimeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_FGConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_FGStart) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_LineWatermarkConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_Suspend) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_exti.o(i.EXTI_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_exti.o(i.EXTI_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_exti.o(i.EXTI_GenerateSWInterrupt) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_exti.o(i.EXTI_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_exti.o(i.EXTI_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_exti.o(i.EXTI_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_DataCacheCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_EraseAllBank1Sectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseAllBank1Sectors) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_EraseAllBank2Sectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseAllBank2Sectors) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_EraseAllSectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseAllSectors) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_EraseSector) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_EraseSector) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_InstructionCacheCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_BORConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_BootConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_Launch) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_PCROP1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_PCROP1Config) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_PCROPConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_PCROPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_PCROPSelectionConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_RDPConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_RDPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_UserConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_UserConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_WRP1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_WRP1Config) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_WRPConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_WRPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_PrefetchBufferCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ProgramByte) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ProgramByte) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramDoubleWord) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ProgramDoubleWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramHalfWord) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramWord) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ProgramWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_SetLatency) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_flash.o(i.FLASH_GetStatus) for FLASH_GetStatus
    stm32f4xx_fsmc.o(i.FSMC_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NANDCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NANDDeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NANDECCCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NANDInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NORSRAMCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NORSRAMDeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NORSRAMInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NORSRAMStructInit) refers to stm32f4xx_fsmc.o(.constdata) for FSMC_DefaultTimingStruct
    stm32f4xx_fsmc.o(i.FSMC_PCCARDCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_PCCARDInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd) for RCC_AHB1PeriphResetCmd
    stm32f4xx_gpio.o(i.GPIO_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_PinAFConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_PinLockConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ReadInputData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ReadOutputData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ReadOutputDataBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ResetBits) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_SetBits) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ToggleBits) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_Write) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_WriteBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_AutoStartDigest) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd) for RCC_AHB2PeriphResetCmd
    stm32f4xx_hash.o(i.HASH_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_DeInit) for HASH_DeInit
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_Init) for HASH_Init
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_DataIn) for HASH_DataIn
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_StartDigest) for HASH_StartDigest
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_GetFlagStatus) for HASH_GetFlagStatus
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_GetDigest) for HASH_GetDigest
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_DeInit) for HASH_DeInit
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_Init) for HASH_Init
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_DataIn) for HASH_DataIn
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_StartDigest) for HASH_StartDigest
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_GetFlagStatus) for HASH_GetFlagStatus
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_GetDigest) for HASH_GetDigest
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_DeInit) for HASH_DeInit
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_Init) for HASH_Init
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_DataIn) for HASH_DataIn
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_StartDigest) for HASH_StartDigest
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_GetFlagStatus) for HASH_GetFlagStatus
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_GetDigest) for HASH_GetDigest
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_DeInit) for HASH_DeInit
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_Init) for HASH_Init
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_DataIn) for HASH_DataIn
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_StartDigest) for HASH_StartDigest
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_GetFlagStatus) for HASH_GetFlagStatus
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_GetDigest) for HASH_GetDigest
    stm32f4xx_i2c.o(i.I2C_ARPCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_AcknowledgeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_AnalogFilterCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_CalculatePEC) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_CheckEvent) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_DMALastTransferCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_i2c.o(i.I2C_DigitalFilterConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_DualAddressCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_FastModeDutyCycleConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_GeneralCallCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_GenerateSTART) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_GenerateSTOP) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_GetLastEvent) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_GetPEC) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_Init) refers to stm32f4xx_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f4xx_i2c.o(i.I2C_NACKPositionConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_OwnAddress2Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_PECPositionConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_ReadRegister) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_ReceiveData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_SMBusAlertConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_Send7bitAddress) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_SendData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_SoftwareResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_StretchClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_TransmitPEC) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_iwdg.o(i.IWDG_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_iwdg.o(i.IWDG_SetPrescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_iwdg.o(i.IWDG_SetReload) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_iwdg.o(i.IWDG_WriteAccessCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_CLUTCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_CLUTInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_ColorKeyingConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_ltdc.o(i.LTDC_DitherCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_GetCDStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_LIPConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_LayerCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_LayerInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_ReloadConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_BackupAccessCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_BackupRegulatorCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_pwr.o(i.PWR_EnterSTOPMode) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_EnterUnderDriveSTOPMode) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_FlashPowerDownCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_MainRegulatorModeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_OverDriveCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_OverDriveSWCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_PVDCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_PVDLevelConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_UnderDriveCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_WakeUpPinCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockLPModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockLPModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockLPModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB3PeriphResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB1PeriphClockLPModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB2PeriphClockLPModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AdjustHSICalibrationValue) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_BackupResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_ClockSecuritySystemCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_GetClocksFreq) refers to stm32f4xx_rcc.o(.data) for APBAHBPrescTable
    stm32f4xx_rcc.o(i.RCC_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_HCLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_HSEConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_HSICmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_I2SCLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_LSEConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_LSEModeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_LSICmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_LTDCCLKDivConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_MCO1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_MCO2Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PCLK1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PCLK2Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLI2SCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLI2SConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLSAICmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLSAIConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_RTCCLKCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_RTCCLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_SAIBlockACLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_SAIBlockBCLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_SAIPLLI2SClkDivConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_SAIPLLSAIClkDivConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_SYSCLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_TIMCLKPresConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f4xx_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f4xx_rng.o(i.RNG_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rng.o(i.RNG_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rng.o(i.RNG_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rng.o(i.RNG_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd) for RCC_AHB2PeriphResetCmd
    stm32f4xx_rng.o(i.RNG_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rng.o(i.RNG_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rng.o(i.RNG_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_AlarmCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_AlarmSubSecondConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_BypassShadowCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_CalibOutputCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_CalibOutputConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_CoarseCalibCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_CoarseCalibCmd) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_CoarseCalibCmd) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_CoarseCalibConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_CoarseCalibConfig) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_CoarseCalibConfig) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_DayLightSavingConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_DeInit) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_DeInit) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(i.RTC_GetAlarm) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_GetAlarm) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_GetDate) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_GetDate) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_GetTime) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_GetTime) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_GetTimeStamp) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_GetTimeStamp) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_Init) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_Init) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_OutputConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_OutputTypeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_ReadBackupRegister) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_RefClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_RefClockCmd) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_RefClockCmd) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_SetAlarm) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_SetAlarm) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_SetAlarm) refers to stm32f4xx_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(i.RTC_SetWakeUpCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_SmoothCalibConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_SynchroShiftConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_SynchroShiftConfig) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(i.RTC_TamperCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TamperFilterConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TamperPinSelection) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TamperPinsPrechargeDuration) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TamperPullUpCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TamperSamplingFreqConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TamperTriggerConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TimeStampCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TimeStampOnTamperDetectionCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TimeStampPinSelection) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_WakeUpClockConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_WakeUpCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_WriteBackupRegister) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_WriteProtectionCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_CompandingModeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_sai.o(i.SAI_FlushFIFO) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_FrameInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_GetCmdStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_GetFIFOStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_MonoModeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_MuteFrameCounterConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_MuteModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_MuteValueConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_ReceiveData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_SendData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_SlotInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_TRIStateConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_CEATAITCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_ClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_CommandCompletionCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_DataConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_sdio.o(i.SDIO_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_GetResponse) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_SendCEATACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_SendCommand) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_SendSDIOSuspendCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_SetPowerState) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_SetSDIOOperation) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_SetSDIOReadWaitMode) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_StartSDIOReadWait) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_StopSDIOReadWait) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.I2S_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.I2S_FullDuplexConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.I2S_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_BiDirectionalLineConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_CalculateCRC) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_DataSizeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_GetCRC) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_GetCRCPolynomial) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_spi.o(i.SPI_I2S_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_spi.o(i.SPI_I2S_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_ReceiveData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_SendData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_NSSInternalSoftwareConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_SSOutputCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_TIModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_TransmitCRC) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_syscfg.o(i.SYSCFG_CompensationCellCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_syscfg.o(i.SYSCFG_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_syscfg.o(i.SYSCFG_ETH_MediaInterfaceConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_syscfg.o(i.SYSCFG_EXTILineConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_syscfg.o(i.SYSCFG_MemoryRemapConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_syscfg.o(i.SYSCFG_MemorySwappingBank) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ARRPreloadConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_BDTRConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_CCPreloadControl) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_CCxCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_CCxNCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ClearOC1Ref) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ClearOC2Ref) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ClearOC3Ref) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ClearOC4Ref) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_CounterModeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_CtrlPWMOutputs) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_DMAConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(i.TIM_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_tim.o(i.TIM_ETRClockMode1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f4xx_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f4xx_tim.o(i.TIM_ETRClockMode2Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f4xx_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f4xx_tim.o(i.TIM_ETRConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_EncoderInterfaceConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ForcedOC1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ForcedOC2Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ForcedOC3Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ForcedOC4Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GenerateEvent) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetCapture1) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetCapture2) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetCapture3) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetCapture4) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetPrescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ICInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI3_Config) for TI3_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI4_Config) for TI4_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f4xx_tim.o(i.TIM_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ITRxExternalClockConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f4xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f4xx_tim.o(i.TIM_InternalClockConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC1FastConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC1Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC1NPolarityConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC1PolarityConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC1PreloadConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC2FastConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC2Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC2NPolarityConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC2PolarityConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC2PreloadConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC3FastConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC3Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC3NPolarityConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC3PolarityConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC3PreloadConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC4FastConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC4Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC4PolarityConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC4PreloadConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f4xx_tim.o(i.TIM_PrescalerConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_RemapConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectCCDMA) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectCOM) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectHallSensor) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectInputTrigger) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectMasterSlaveMode) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectOCxM) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectOnePulseMode) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectOutputTrigger) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectSlaveMode) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetAutoreload) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetClockDivision) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetCompare1) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetCompare2) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetCompare3) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetCompare4) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetIC1Prescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetIC2Prescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetIC3Prescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetIC4Prescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f4xx_tim.o(i.TIM_TimeBaseInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_UpdateDisableConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_UpdateRequestConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_ClockInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(i.USART_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_usart.o(i.USART_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_HalfDuplexCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_Init) refers to stm32f4xx_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f4xx_usart.o(i.USART_IrDACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_IrDAConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_LINBreakDetectLengthConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_LINCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_OneBitMethodCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_OverSampling8Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_ReceiveData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_ReceiverWakeUpCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SendBreak) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SendData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SetAddress) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SetGuardTime) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SetPrescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SmartCardCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SmartCardNACKCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_WakeUpConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_wwdg.o(i.WWDG_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_wwdg.o(i.WWDG_Enable) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_wwdg.o(i.WWDG_SetCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_wwdg.o(i.WWDG_SetPrescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_wwdg.o(i.WWDG_SetWindowValue) refers to main.o(i.assert_failed) for assert_failed
    systick.o(i.DWT_Init) refers to systick.o(.data) for s_dwt_initialized
    systick.o(i.Delay_ms) refers to systick.o(i.SysTick_UpdateStats) for SysTick_UpdateStats
    systick.o(i.Delay_ms) refers to systick.o(.data) for s_delay_counter
    systick.o(i.Delay_s) refers to systick.o(i.Delay_ms) for Delay_ms
    systick.o(i.Delay_us) refers to systick.o(i.SysTick_GetCalibratedDelay) for SysTick_GetCalibratedDelay
    systick.o(i.Delay_us) refers to systick.o(i.SysTick_UpdateStats) for SysTick_UpdateStats
    systick.o(i.Delay_us) refers to systick.o(.data) for s_dwt_initialized
    systick.o(i.SysTick_Calibrate) refers to systick.o(i.Delay_us) for Delay_us
    systick.o(i.SysTick_Calibrate) refers to systick.o(.data) for g_systick_cal
    systick.o(i.SysTick_GetCalibratedDelay) refers to systick.o(.data) for g_systick_cal
    systick.o(i.SysTick_GetStats) refers to systick.o(.bss) for g_systick_stats
    systick.o(i.SysTick_GetTick) refers to systick.o(.data) for g_systick_counter
    systick.o(i.SysTick_GetTimestamp_us) refers to systick.o(.data) for g_system_uptime_ms
    systick.o(i.SysTick_GetUptime_ms) refers to systick.o(.data) for g_system_uptime_ms
    systick.o(i.SysTick_Handler_Internal) refers to systick.o(.data) for g_systick_counter
    systick.o(i.SysTick_Init) refers to systick.o(i.NVIC_SetPriority) for NVIC_SetPriority
    systick.o(i.SysTick_Init) refers to systick.o(i.DWT_Init) for DWT_Init
    systick.o(i.SysTick_Init) refers to systick.o(i.SysTick_ResetStats) for SysTick_ResetStats
    systick.o(i.SysTick_Init) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    systick.o(i.SysTick_Init) refers to systick.o(.data) for g_systick_cal
    systick.o(i.SysTick_ResetStats) refers to systick.o(.bss) for g_systick_stats
    systick.o(i.SysTick_SetTemperatureCompensation) refers to systick.o(.data) for g_systick_cal
    systick.o(i.SysTick_UpdateStats) refers to systick.o(.bss) for g_systick_stats
    systick.o(i.SysTick_UpdateStats) refers to systick.o(.data) for g_system_uptime_ms
    ad9910_hal.o(i.AD9910_HAL_DelayMs) refers to systick.o(i.Delay_ms) for Delay_ms
    ad9910_hal.o(i.AD9910_HAL_IOUpdate) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ad9910_hal.o(i.AD9910_HAL_IOUpdate) refers to ad9910_hal.o(i.AD9910_HAL_DelayUs) for AD9910_HAL_DelayUs
    ad9910_hal.o(i.AD9910_HAL_IOUpdate) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ad9910_hal.o(i.AD9910_HAL_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    ad9910_hal.o(i.AD9910_HAL_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    ad9910_hal.o(i.AD9910_HAL_Init) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ad9910_hal.o(i.AD9910_HAL_Init) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ad9910_hal.o(i.AD9910_HAL_PowerControl) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ad9910_hal.o(i.AD9910_HAL_PowerControl) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ad9910_hal.o(i.AD9910_HAL_Reset) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ad9910_hal.o(i.AD9910_HAL_Reset) refers to ad9910_hal.o(i.AD9910_HAL_DelayMs) for AD9910_HAL_DelayMs
    ad9910_hal.o(i.AD9910_HAL_Reset) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ad9910_hal.o(i.AD9910_HAL_SelectProfile) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ad9910_hal.o(i.AD9910_HAL_SelectProfile) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ad9910_hal.o(i.AD9910_HAL_Send8Bits) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ad9910_hal.o(i.AD9910_HAL_Send8Bits) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ad9910_waveform.o(i.AD9910_CalculateAmplitudeWord) refers to ad9910_waveform.o(i.AD9910_CalculateAmplitudeWord_Precision) for AD9910_CalculateAmplitudeWord_Precision
    ad9910_waveform.o(i.AD9910_CalculateAmplitudeWord_Precision) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    ad9910_waveform.o(i.AD9910_CalculateAmplitudeWord_Precision) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    ad9910_waveform.o(i.AD9910_CalculateAmplitudeWord_Precision) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    ad9910_waveform.o(i.AD9910_CalculateAmplitudeWord_Precision) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    ad9910_waveform.o(i.AD9910_CalculateAmplitudeWord_Precision) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    ad9910_waveform.o(i.AD9910_CalculateAmplitudeWord_Precision) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    ad9910_waveform.o(i.AD9910_CalculateAmplitudeWord_Precision) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    ad9910_waveform.o(i.AD9910_CalculateAmplitudeWord_Precision) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    ad9910_waveform.o(i.AD9910_CalculateAmplitudeWord_Precision) refers to ad9910_waveform.o(.data) for amplitude_calibration_factor
    ad9910_waveform.o(i.AD9910_CalculateAmplitudeWord_Precision) refers to ad9910_waveform.o(.bss) for current_config
    ad9910_waveform.o(i.AD9910_CalculateFrequencyWord) refers to ad9910_waveform.o(i.AD9910_CalculateFrequencyWord_Precision) for AD9910_CalculateFrequencyWord_Precision
    ad9910_waveform.o(i.AD9910_CalculateFrequencyWord_Precision) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    ad9910_waveform.o(i.AD9910_CalculateFrequencyWord_Precision) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    ad9910_waveform.o(i.AD9910_CalculateFrequencyWord_Precision) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    ad9910_waveform.o(i.AD9910_CalculateFrequencyWord_Precision) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    ad9910_waveform.o(i.AD9910_CalculateFrequencyWord_Precision) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    ad9910_waveform.o(i.AD9910_CalculateFrequencyWord_Precision) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    ad9910_waveform.o(i.AD9910_CalculateFrequencyWord_Precision) refers to ad9910_waveform.o(.data) for frequency_calibration_factor
    ad9910_waveform.o(i.AD9910_Configure) refers to ad9910_waveform.o(i.AD9910_SetFrequency) for AD9910_SetFrequency
    ad9910_waveform.o(i.AD9910_Configure) refers to ad9910_waveform.o(i.AD9910_SetAmplitude) for AD9910_SetAmplitude
    ad9910_waveform.o(i.AD9910_Configure) refers to ad9910_waveform.o(i.AD9910_SetPhase) for AD9910_SetPhase
    ad9910_waveform.o(i.AD9910_Configure) refers to ad9910_waveform.o(i.AD9910_SetWaveType) for AD9910_SetWaveType
    ad9910_waveform.o(i.AD9910_Configure) refers to ad9910_hal.o(i.AD9910_HAL_SelectProfile) for AD9910_HAL_SelectProfile
    ad9910_waveform.o(i.AD9910_DisableOutput) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ad9910_waveform.o(i.AD9910_EnableInverseSincFilter) refers to ad9910_waveform.o(i.AD9910_WriteRegister) for AD9910_WriteRegister
    ad9910_waveform.o(i.AD9910_EnableInverseSincFilter) refers to ad9910_hal.o(i.AD9910_HAL_IOUpdate) for AD9910_HAL_IOUpdate
    ad9910_waveform.o(i.AD9910_EnableInverseSincFilter) refers to ad9910_waveform.o(.constdata) for cfr1_data
    ad9910_waveform.o(i.AD9910_EnableOutput) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ad9910_waveform.o(i.AD9910_EnsurePhaseContinuity) refers to ad9910_waveform.o(i.AD9910_WriteRegister) for AD9910_WriteRegister
    ad9910_waveform.o(i.AD9910_EnsurePhaseContinuity) refers to ad9910_hal.o(i.AD9910_HAL_IOUpdate) for AD9910_HAL_IOUpdate
    ad9910_waveform.o(i.AD9910_EnsurePhaseContinuity) refers to ad9910_waveform.o(.constdata) for cfr1_data
    ad9910_waveform.o(i.AD9910_GetConfiguration) refers to ad9910_waveform.o(.bss) for current_config
    ad9910_waveform.o(i.AD9910_Init) refers to ad9910_hal.o(i.AD9910_HAL_Init) for AD9910_HAL_Init
    ad9910_waveform.o(i.AD9910_Init) refers to ad9910_hal.o(i.AD9910_HAL_PowerControl) for AD9910_HAL_PowerControl
    ad9910_waveform.o(i.AD9910_Init) refers to ad9910_hal.o(i.AD9910_HAL_SelectProfile) for AD9910_HAL_SelectProfile
    ad9910_waveform.o(i.AD9910_Init) refers to ad9910_hal.o(i.AD9910_HAL_Reset) for AD9910_HAL_Reset
    ad9910_waveform.o(i.AD9910_Init) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ad9910_waveform.o(i.AD9910_Init) refers to ad9910_hal.o(i.AD9910_HAL_Send8Bits) for AD9910_HAL_Send8Bits
    ad9910_waveform.o(i.AD9910_Init) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ad9910_waveform.o(i.AD9910_Init) refers to ad9910_hal.o(i.AD9910_HAL_DelayUs) for AD9910_HAL_DelayUs
    ad9910_waveform.o(i.AD9910_Init) refers to ad9910_hal.o(i.AD9910_HAL_IOUpdate) for AD9910_HAL_IOUpdate
    ad9910_waveform.o(i.AD9910_Init) refers to ad9910_hal.o(i.AD9910_HAL_DelayMs) for AD9910_HAL_DelayMs
    ad9910_waveform.o(i.AD9910_Init) refers to ad9910_waveform.o(.constdata) for cfr1_data
    ad9910_waveform.o(i.AD9910_Init) refers to ad9910_waveform.o(.bss) for current_config
    ad9910_waveform.o(i.AD9910_OptimizeSmoothness) refers to ad9910_waveform.o(i.AD9910_EnableInverseSincFilter) for AD9910_EnableInverseSincFilter
    ad9910_waveform.o(i.AD9910_OptimizeSmoothness) refers to ad9910_waveform.o(i.AD9910_EnsurePhaseContinuity) for AD9910_EnsurePhaseContinuity
    ad9910_waveform.o(i.AD9910_OptimizeSmoothness) refers to ad9910_waveform.o(i.AD9910_OptimizeOutputFilter) for AD9910_OptimizeOutputFilter
    ad9910_waveform.o(i.AD9910_OptimizeSmoothness) refers to ad9910_hal.o(i.AD9910_HAL_DelayMs) for AD9910_HAL_DelayMs
    ad9910_waveform.o(i.AD9910_OptimizeWaveform) refers to ad9910_waveform.o(.data) for frequency_calibration_factor
    ad9910_waveform.o(i.AD9910_Set5MHz_HighQuality) refers to ad9910_waveform.o(i.AD9910_OptimizeWaveform) for AD9910_OptimizeWaveform
    ad9910_waveform.o(i.AD9910_Set5MHz_HighQuality) refers to ad9910_waveform.o(i.AD9910_OptimizeSmoothness) for AD9910_OptimizeSmoothness
    ad9910_waveform.o(i.AD9910_Set5MHz_HighQuality) refers to ad9910_waveform.o(i.AD9910_SetFrequency_Precision) for AD9910_SetFrequency_Precision
    ad9910_waveform.o(i.AD9910_Set5MHz_HighQuality) refers to ad9910_waveform.o(i.AD9910_SetAmplitude_Precision) for AD9910_SetAmplitude_Precision
    ad9910_waveform.o(i.AD9910_Set5MHz_HighQuality) refers to ad9910_hal.o(i.AD9910_HAL_DelayMs) for AD9910_HAL_DelayMs
    ad9910_waveform.o(i.AD9910_SetAmplitude) refers to ad9910_waveform.o(i.AD9910_CalculateAmplitudeWord) for AD9910_CalculateAmplitudeWord
    ad9910_waveform.o(i.AD9910_SetAmplitude) refers to ad9910_waveform.o(i.AD9910_WriteProfile) for AD9910_WriteProfile
    ad9910_waveform.o(i.AD9910_SetAmplitude) refers to ad9910_waveform.o(.data) for profile_data
    ad9910_waveform.o(i.AD9910_SetAmplitude) refers to ad9910_waveform.o(.bss) for current_config
    ad9910_waveform.o(i.AD9910_SetAmplitudeCalibration) refers to ad9910_waveform.o(.data) for amplitude_calibration_factor
    ad9910_waveform.o(i.AD9910_SetAmplitude_Precision) refers to ad9910_waveform.o(i.AD9910_CalculateAmplitudeWord_Precision) for AD9910_CalculateAmplitudeWord_Precision
    ad9910_waveform.o(i.AD9910_SetAmplitude_Precision) refers to ad9910_waveform.o(i.AD9910_WriteProfile) for AD9910_WriteProfile
    ad9910_waveform.o(i.AD9910_SetAmplitude_Precision) refers to ad9910_waveform.o(.data) for profile_data
    ad9910_waveform.o(i.AD9910_SetAmplitude_Precision) refers to ad9910_waveform.o(.bss) for current_config
    ad9910_waveform.o(i.AD9910_SetFrequency) refers to ad9910_waveform.o(i.AD9910_CalculateFrequencyWord) for AD9910_CalculateFrequencyWord
    ad9910_waveform.o(i.AD9910_SetFrequency) refers to ad9910_waveform.o(i.AD9910_WriteProfile) for AD9910_WriteProfile
    ad9910_waveform.o(i.AD9910_SetFrequency) refers to ad9910_waveform.o(.data) for profile_data
    ad9910_waveform.o(i.AD9910_SetFrequency) refers to ad9910_waveform.o(.bss) for current_config
    ad9910_waveform.o(i.AD9910_SetFrequencyCalibration) refers to ad9910_waveform.o(.data) for frequency_calibration_factor
    ad9910_waveform.o(i.AD9910_SetFrequency_Precision) refers to ad9910_waveform.o(i.AD9910_CalculateFrequencyWord_Precision) for AD9910_CalculateFrequencyWord_Precision
    ad9910_waveform.o(i.AD9910_SetFrequency_Precision) refers to ad9910_waveform.o(i.AD9910_WriteProfile) for AD9910_WriteProfile
    ad9910_waveform.o(i.AD9910_SetFrequency_Precision) refers to ad9910_waveform.o(.data) for profile_data
    ad9910_waveform.o(i.AD9910_SetFrequency_Precision) refers to ad9910_waveform.o(.bss) for current_config
    ad9910_waveform.o(i.AD9910_SetPhase) refers to ad9910_waveform.o(i.AD9910_WriteProfile) for AD9910_WriteProfile
    ad9910_waveform.o(i.AD9910_SetPhase) refers to ad9910_waveform.o(.data) for profile_data
    ad9910_waveform.o(i.AD9910_SetPhase) refers to ad9910_waveform.o(.bss) for current_config
    ad9910_waveform.o(i.AD9910_SetTemperatureCompensation) refers to ad9910_waveform.o(.data) for temperature_offset
    ad9910_waveform.o(i.AD9910_SetWaveType) refers to ad9910_waveform.o(.bss) for current_config
    ad9910_waveform.o(i.AD9910_SoftwareReset) refers to ad9910_hal.o(i.AD9910_HAL_Reset) for AD9910_HAL_Reset
    ad9910_waveform.o(i.AD9910_SoftwareReset) refers to ad9910_waveform.o(i.AD9910_Init) for AD9910_Init
    ad9910_waveform.o(i.AD9910_WriteProfile) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ad9910_waveform.o(i.AD9910_WriteProfile) refers to ad9910_hal.o(i.AD9910_HAL_Send8Bits) for AD9910_HAL_Send8Bits
    ad9910_waveform.o(i.AD9910_WriteProfile) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ad9910_waveform.o(i.AD9910_WriteProfile) refers to ad9910_hal.o(i.AD9910_HAL_IOUpdate) for AD9910_HAL_IOUpdate
    ad9910_waveform.o(i.AD9910_WriteProfile) refers to ad9910_waveform.o(.data) for profile_data
    ad9910_waveform.o(i.AD9910_WriteRegister) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ad9910_waveform.o(i.AD9910_WriteRegister) refers to ad9910_hal.o(i.AD9910_HAL_Send8Bits) for AD9910_HAL_Send8Bits
    ad9910_waveform.o(i.AD9910_WriteRegister) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ad9910_waveform.o(i.AD9910_WriteRegister) refers to ad9910_hal.o(i.AD9910_HAL_DelayUs) for AD9910_HAL_DelayUs
    ad9910_control.o(i.AD9910_Control_CalculateActualOutput) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    ad9910_control.o(i.AD9910_Control_CalculateActualOutput) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    ad9910_control.o(i.AD9910_Control_CalculateActualOutput) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    ad9910_control.o(i.AD9910_Control_CalculateActualOutput) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    ad9910_control.o(i.AD9910_Control_CalculateActualOutput) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    ad9910_control.o(i.AD9910_Control_CalculateActualOutput) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    ad9910_control.o(i.AD9910_Control_CalculateTargetOutput) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    ad9910_control.o(i.AD9910_Control_CalculateTargetOutput) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    ad9910_control.o(i.AD9910_Control_CalculateTargetOutput) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    ad9910_control.o(i.AD9910_Control_CalculateTargetOutput) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    ad9910_control.o(i.AD9910_Control_EnableOutput) refers to ad9910_waveform.o(i.AD9910_EnableOutput) for AD9910_EnableOutput
    ad9910_control.o(i.AD9910_Control_EnableOutput) refers to ad9910_waveform.o(i.AD9910_DisableOutput) for AD9910_DisableOutput
    ad9910_control.o(i.AD9910_Control_EnableOutput) refers to ad9910_control.o(i.UpdateSystemTime) for UpdateSystemTime
    ad9910_control.o(i.AD9910_Control_EnableOutput) refers to ad9910_control.o(.bss) for system_params
    ad9910_control.o(i.AD9910_Control_Execute) refers to ad9910_control.o(i.AD9910_Control_SetFrequency) for AD9910_Control_SetFrequency
    ad9910_control.o(i.AD9910_Control_Execute) refers to ad9910_control.o(i.AD9910_Control_SetTargetAmplitude) for AD9910_Control_SetTargetAmplitude
    ad9910_control.o(i.AD9910_Control_Execute) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    ad9910_control.o(i.AD9910_Control_Execute) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    ad9910_control.o(i.AD9910_Control_Execute) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    ad9910_control.o(i.AD9910_Control_Execute) refers to ad9910_control.o(i.AD9910_Control_SetGainFactor) for AD9910_Control_SetGainFactor
    ad9910_control.o(i.AD9910_Control_Execute) refers to ad9910_control.o(i.AD9910_Control_EnableOutput) for AD9910_Control_EnableOutput
    ad9910_control.o(i.AD9910_Control_Execute) refers to ad9910_waveform.o(i.AD9910_SoftwareReset) for AD9910_SoftwareReset
    ad9910_control.o(i.AD9910_Control_Execute) refers to ad9910_control.o(i.AD9910_Control_Init) for AD9910_Control_Init
    ad9910_control.o(i.AD9910_Control_Execute) refers to ad9910_control.o(i.AD9910_Control_LoadPreset) for AD9910_Control_LoadPreset
    ad9910_control.o(i.AD9910_Control_Execute) refers to ad9910_control.o(.data) for system_initialized
    ad9910_control.o(i.AD9910_Control_Execute) refers to ad9910_control.o(.bss) for system_params
    ad9910_control.o(i.AD9910_Control_GetParams) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    ad9910_control.o(i.AD9910_Control_GetParams) refers to ad9910_control.o(.bss) for system_params
    ad9910_control.o(i.AD9910_Control_GetRanges) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    ad9910_control.o(i.AD9910_Control_GetRanges) refers to ad9910_control.o(.data) for param_ranges
    ad9910_control.o(i.AD9910_Control_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    ad9910_control.o(i.AD9910_Control_Init) refers to strcpy.o(.text) for strcpy
    ad9910_control.o(i.AD9910_Control_Init) refers to ad9910_waveform.o(i.AD9910_Init) for AD9910_Init
    ad9910_control.o(i.AD9910_Control_Init) refers to ad9910_waveform.o(i.AD9910_Set5MHz_HighQuality) for AD9910_Set5MHz_HighQuality
    ad9910_control.o(i.AD9910_Control_Init) refers to ad9910_control.o(.bss) for system_params
    ad9910_control.o(i.AD9910_Control_Init) refers to ad9910_control.o(.data) for system_initialized
    ad9910_control.o(i.AD9910_Control_LoadPreset) refers to ad9910_control.o(i.AD9910_Control_SetGainFactor) for AD9910_Control_SetGainFactor
    ad9910_control.o(i.AD9910_Control_LoadPreset) refers to ad9910_control.o(i.AD9910_Control_SetFrequency) for AD9910_Control_SetFrequency
    ad9910_control.o(i.AD9910_Control_LoadPreset) refers to ad9910_control.o(i.AD9910_Control_SetTargetAmplitude) for AD9910_Control_SetTargetAmplitude
    ad9910_control.o(i.AD9910_Control_LoadPreset) refers to ad9910_control.o(.bss) for presets
    ad9910_control.o(i.AD9910_Control_RegisterCallback) refers to ad9910_control.o(.data) for control_callback
    ad9910_control.o(i.AD9910_Control_SetFrequency) refers to ad9910_control.o(i.ValidateFrequency) for ValidateFrequency
    ad9910_control.o(i.AD9910_Control_SetFrequency) refers to ad9910_waveform.o(i.AD9910_SetFrequency_Precision) for AD9910_SetFrequency_Precision
    ad9910_control.o(i.AD9910_Control_SetFrequency) refers to ad9910_control.o(i.UpdateSystemTime) for UpdateSystemTime
    ad9910_control.o(i.AD9910_Control_SetFrequency) refers to ad9910_control.o(.bss) for system_params
    ad9910_control.o(i.AD9910_Control_SetGainFactor) refers to ad9910_control.o(i.ValidateGainFactor) for ValidateGainFactor
    ad9910_control.o(i.AD9910_Control_SetGainFactor) refers to ad9910_control.o(i.AD9910_Control_CalculateActualOutput) for AD9910_Control_CalculateActualOutput
    ad9910_control.o(i.AD9910_Control_SetGainFactor) refers to ad9910_waveform.o(i.AD9910_SetAmplitude_Precision) for AD9910_SetAmplitude_Precision
    ad9910_control.o(i.AD9910_Control_SetGainFactor) refers to ad9910_control.o(i.AD9910_Control_CalculateTargetOutput) for AD9910_Control_CalculateTargetOutput
    ad9910_control.o(i.AD9910_Control_SetGainFactor) refers to ad9910_control.o(i.UpdateSystemTime) for UpdateSystemTime
    ad9910_control.o(i.AD9910_Control_SetGainFactor) refers to ad9910_control.o(.bss) for system_params
    ad9910_control.o(i.AD9910_Control_SetPreset) refers to ad9910_control.o(i.AD9910_Control_ValidateParams) for AD9910_Control_ValidateParams
    ad9910_control.o(i.AD9910_Control_SetPreset) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    ad9910_control.o(i.AD9910_Control_SetPreset) refers to ad9910_control.o(.bss) for presets
    ad9910_control.o(i.AD9910_Control_SetTargetAmplitude) refers to ad9910_control.o(i.ValidateAmplitude) for ValidateAmplitude
    ad9910_control.o(i.AD9910_Control_SetTargetAmplitude) refers to ad9910_control.o(i.AD9910_Control_CalculateActualOutput) for AD9910_Control_CalculateActualOutput
    ad9910_control.o(i.AD9910_Control_SetTargetAmplitude) refers to ad9910_waveform.o(i.AD9910_SetAmplitude_Precision) for AD9910_SetAmplitude_Precision
    ad9910_control.o(i.AD9910_Control_SetTargetAmplitude) refers to ad9910_control.o(i.UpdateSystemTime) for UpdateSystemTime
    ad9910_control.o(i.AD9910_Control_SetTargetAmplitude) refers to ad9910_control.o(.bss) for system_params
    ad9910_control.o(i.AD9910_Control_ValidateParams) refers to ad9910_control.o(i.ValidateFrequency) for ValidateFrequency
    ad9910_control.o(i.AD9910_Control_ValidateParams) refers to ad9910_control.o(i.ValidateAmplitude) for ValidateAmplitude
    ad9910_control.o(i.AD9910_Control_ValidateParams) refers to ad9910_control.o(i.ValidateGainFactor) for ValidateGainFactor
    ad9910_control.o(i.UpdateSystemTime) refers to systick.o(i.SysTick_GetTick) for SysTick_GetTick
    ad9910_control.o(i.UpdateSystemTime) refers to ad9910_control.o(.bss) for system_params
    ad9910_control.o(i.ValidateAmplitude) refers to ad9910_control.o(.data) for param_ranges
    ad9910_control.o(i.ValidateFrequency) refers to ad9910_control.o(.data) for param_ranges
    ad9910_control.o(i.ValidateGainFactor) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    ad9910_control.o(i.ValidateGainFactor) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    ad9910_control.o(i.ValidateGainFactor) refers to ad9910_control.o(.data) for param_ranges
    command_parser.o(i.CommandParser_AddUARTData) refers to command_parser.o(.data) for uart_buffer_pos
    command_parser.o(i.CommandParser_AddUARTData) refers to command_parser.o(.bss) for uart_buffer
    command_parser.o(i.CommandParser_ClearBuffer) refers to rt_memclr.o(.text) for __aeabi_memclr
    command_parser.o(i.CommandParser_ClearBuffer) refers to command_parser.o(.bss) for uart_buffer
    command_parser.o(i.CommandParser_ClearBuffer) refers to command_parser.o(.data) for uart_buffer_pos
    command_parser.o(i.CommandParser_FormatKeypadResponse) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    command_parser.o(i.CommandParser_FormatKeypadResponse) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    command_parser.o(i.CommandParser_FormatKeypadResponse) refers to _printf_str.o(.text) for _printf_str
    command_parser.o(i.CommandParser_FormatKeypadResponse) refers to __2snprintf.o(.text) for __2snprintf
    command_parser.o(i.CommandParser_FormatUARTResponse) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    command_parser.o(i.CommandParser_FormatUARTResponse) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    command_parser.o(i.CommandParser_FormatUARTResponse) refers to _printf_str.o(.text) for _printf_str
    command_parser.o(i.CommandParser_FormatUARTResponse) refers to __2snprintf.o(.text) for __2snprintf
    command_parser.o(i.CommandParser_GenerateResponse) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    command_parser.o(i.CommandParser_GenerateResponse) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    command_parser.o(i.CommandParser_GenerateResponse) refers to _printf_dec.o(.text) for _printf_int_dec
    command_parser.o(i.CommandParser_GenerateResponse) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    command_parser.o(i.CommandParser_GenerateResponse) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    command_parser.o(i.CommandParser_GenerateResponse) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    command_parser.o(i.CommandParser_GenerateResponse) refers to __2snprintf.o(.text) for __2snprintf
    command_parser.o(i.CommandParser_GenerateResponse) refers to strcpy.o(.text) for strcpy
    command_parser.o(i.CommandParser_GenerateResponse) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    command_parser.o(i.CommandParser_GenerateResponse) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    command_parser.o(i.CommandParser_GenerateResponse) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    command_parser.o(i.CommandParser_GenerateResponse) refers to ad9910_control.o(i.AD9910_Control_GetParams) for AD9910_Control_GetParams
    command_parser.o(i.CommandParser_GenerateResponse) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    command_parser.o(i.CommandParser_GenerateResponse) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    command_parser.o(i.CommandParser_Init) refers to rt_memclr.o(.text) for __aeabi_memclr
    command_parser.o(i.CommandParser_Init) refers to command_parser.o(.bss) for uart_buffer
    command_parser.o(i.CommandParser_Init) refers to command_parser.o(.data) for uart_buffer_pos
    command_parser.o(i.CommandParser_ParseKeypad) refers to command_parser.o(i.ParseKeypadSequence) for ParseKeypadSequence
    command_parser.o(i.CommandParser_ParseUART) refers to command_parser.o(i.CommandParser_AddUARTData) for CommandParser_AddUARTData
    command_parser.o(i.CommandParser_ParseUART) refers to command_parser.o(i.CommandParser_ProcessPendingUART) for CommandParser_ProcessPendingUART
    command_parser.o(i.CommandParser_ProcessPendingUART) refers to strchr.o(.text) for strchr
    command_parser.o(i.CommandParser_ProcessPendingUART) refers to command_parser.o(i.ParseUARTCommand) for ParseUARTCommand
    command_parser.o(i.CommandParser_ProcessPendingUART) refers to rt_memmove_v6.o(.text) for __aeabi_memmove
    command_parser.o(i.CommandParser_ProcessPendingUART) refers to command_parser.o(i.CommandParser_ClearBuffer) for CommandParser_ClearBuffer
    command_parser.o(i.CommandParser_ProcessPendingUART) refers to command_parser.o(.bss) for uart_buffer
    command_parser.o(i.CommandParser_ProcessPendingUART) refers to command_parser.o(.data) for uart_buffer_pos
    command_parser.o(i.ParseDoubleParameter) refers to strtod.o(i.__hardfp_strtod) for __hardfp_strtod
    command_parser.o(i.ParseKeypadSequence) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    command_parser.o(i.ParseKeypadSequence) refers to systick.o(i.SysTick_GetTick) for SysTick_GetTick
    command_parser.o(i.ParseKeypadSequence) refers to ad9910_control.o(i.AD9910_Control_GetParams) for AD9910_Control_GetParams
    command_parser.o(i.ParseUARTCommand) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    command_parser.o(i.ParseUARTCommand) refers to systick.o(i.SysTick_GetTick) for SysTick_GetTick
    command_parser.o(i.ParseUARTCommand) refers to strchr.o(.text) for strchr
    command_parser.o(i.ParseUARTCommand) refers to strncmp.o(.text) for strncmp
    command_parser.o(i.ParseUARTCommand) refers to strtoul.o(.text) for strtoul
    command_parser.o(i.ParseUARTCommand) refers to command_parser.o(i.ParseDoubleParameter) for ParseDoubleParameter
    command_parser.o(i.ParseUARTCommand) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    command_parser.o(i.ParseUARTCommand) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    command_parser.o(i.ParseUARTCommand) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    command_parser.o(i.ParseUARTCommand) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    gain_calculator.o(i.CalculateTotalGain) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    gain_calculator.o(i.GainCalculator_Calculate) refers to gain_calculator.o(i.ValidateGainConfig) for ValidateGainConfig
    gain_calculator.o(i.GainCalculator_Calculate) refers to gain_calculator.o(i.GainCalculator_FrequencyCorrection) for GainCalculator_FrequencyCorrection
    gain_calculator.o(i.GainCalculator_Calculate) refers to gain_calculator.o(i.GainCalculator_TemperatureCorrection) for GainCalculator_TemperatureCorrection
    gain_calculator.o(i.GainCalculator_Calculate) refers to gain_calculator.o(i.GainCalculator_TransferFunctionGain) for GainCalculator_TransferFunctionGain
    gain_calculator.o(i.GainCalculator_Calculate) refers to gain_calculator.o(i.GainCalculator_NonlinearCorrection) for GainCalculator_NonlinearCorrection
    gain_calculator.o(i.GainCalculator_Calculate) refers to gain_calculator.o(i.CalculateTotalGain) for CalculateTotalGain
    gain_calculator.o(i.GainCalculator_Calculate) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    gain_calculator.o(i.GainCalculator_Calculate) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    gain_calculator.o(i.GainCalculator_Calculate) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    gain_calculator.o(i.GainCalculator_Calculate) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    gain_calculator.o(i.GainCalculator_Calculate) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    gain_calculator.o(i.GainCalculator_Calculate) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    gain_calculator.o(i.GainCalculator_Calculate) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    gain_calculator.o(i.GainCalculator_Calculate) refers to gain_calculator.o(.data) for gain_calc_initialized
    gain_calculator.o(i.GainCalculator_Calculate) refers to gain_calculator.o(.bss) for current_stages
    gain_calculator.o(i.GainCalculator_CalculateDynamicRange) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    gain_calculator.o(i.GainCalculator_CalculateDynamicRange) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    gain_calculator.o(i.GainCalculator_CalculateDynamicRange) refers to log10.o(i.__hardfp_log10) for __hardfp_log10
    gain_calculator.o(i.GainCalculator_CalculateDynamicRange) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    gain_calculator.o(i.GainCalculator_ConfigForContest) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    gain_calculator.o(i.GainCalculator_ConfigForContest) refers to gain_calculator.o(i.GainCalculator_ValidateGain) for GainCalculator_ValidateGain
    gain_calculator.o(i.GainCalculator_ConfigForContest) refers to gain_calculator.o(.bss) for current_stages
    gain_calculator.o(i.GainCalculator_FrequencyCorrection) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    gain_calculator.o(i.GainCalculator_FrequencyCorrection) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    gain_calculator.o(i.GainCalculator_FrequencyCorrection) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    gain_calculator.o(i.GainCalculator_FrequencyCorrection) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    gain_calculator.o(i.GainCalculator_FrequencyCorrection) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    gain_calculator.o(i.GainCalculator_FrequencyCorrection) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    gain_calculator.o(i.GainCalculator_GetAD9910Output) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    gain_calculator.o(i.GainCalculator_GetAD9910Output) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    gain_calculator.o(i.GainCalculator_GetAD9910Output) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    gain_calculator.o(i.GainCalculator_GetAD9910Output) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    gain_calculator.o(i.GainCalculator_GetAD9910Output) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    gain_calculator.o(i.GainCalculator_GetAD9910Output) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    gain_calculator.o(i.GainCalculator_GetFinalOutput) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    gain_calculator.o(i.GainCalculator_GetFinalOutput) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    gain_calculator.o(i.GainCalculator_GetFinalOutput) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    gain_calculator.o(i.GainCalculator_GetFinalOutput) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    gain_calculator.o(i.GainCalculator_GetStages) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    gain_calculator.o(i.GainCalculator_GetStages) refers to gain_calculator.o(.bss) for current_stages
    gain_calculator.o(i.GainCalculator_Init) refers to gain_calculator.o(.bss) for current_stages
    gain_calculator.o(i.GainCalculator_Init) refers to gain_calculator.o(.data) for gain_calc_initialized
    gain_calculator.o(i.GainCalculator_OptimizeAccuracy) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    gain_calculator.o(i.GainCalculator_OptimizeStages) refers to gain_calculator.o(i.GainCalculator_ValidateGain) for GainCalculator_ValidateGain
    gain_calculator.o(i.GainCalculator_OptimizeStages) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    gain_calculator.o(i.GainCalculator_OptimizeStages) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    gain_calculator.o(i.GainCalculator_OptimizeStages) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    gain_calculator.o(i.GainCalculator_OptimizeStages) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    gain_calculator.o(i.GainCalculator_OptimizeStages) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    gain_calculator.o(i.GainCalculator_OptimizeStages) refers to gain_calculator.o(.bss) for current_stages
    gain_calculator.o(i.GainCalculator_SelfTest) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    gain_calculator.o(i.GainCalculator_SelfTest) refers to gain_calculator.o(i.GainCalculator_Calculate) for GainCalculator_Calculate
    gain_calculator.o(i.GainCalculator_SelfTest) refers to gain_calculator.o(.constdata) for .constdata
    gain_calculator.o(i.GainCalculator_SetVariableGain) refers to gain_calculator.o(i.GainCalculator_ValidateGain) for GainCalculator_ValidateGain
    gain_calculator.o(i.GainCalculator_SetVariableGain) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    gain_calculator.o(i.GainCalculator_SetVariableGain) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    gain_calculator.o(i.GainCalculator_SetVariableGain) refers to gain_calculator.o(.bss) for current_stages
    gain_calculator.o(i.GainCalculator_TemperatureCorrection) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    gain_calculator.o(i.GainCalculator_TemperatureCorrection) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    gain_calculator.o(i.GainCalculator_TemperatureCorrection) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    gain_calculator.o(i.GainCalculator_TemperatureCorrection) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    gain_calculator.o(i.GainCalculator_TemperatureCorrection) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    gain_calculator.o(i.GainCalculator_TemperatureCorrection) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    gain_calculator.o(i.GainCalculator_TransferFunctionGain) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    gain_calculator.o(i.GainCalculator_TransferFunctionGain) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    gain_calculator.o(i.GainCalculator_TransferFunctionGain) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    gain_calculator.o(i.GainCalculator_TransferFunctionGain) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    gain_calculator.o(i.GainCalculator_TransferFunctionGain) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    gain_calculator.o(i.GainCalculator_TransferFunctionGain) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    gain_calculator.o(i.GainCalculator_TransferFunctionPhase) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    gain_calculator.o(i.GainCalculator_TransferFunctionPhase) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    gain_calculator.o(i.GainCalculator_TransferFunctionPhase) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    gain_calculator.o(i.GainCalculator_TransferFunctionPhase) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    gain_calculator.o(i.GainCalculator_TransferFunctionPhase) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    gain_calculator.o(i.GainCalculator_TransferFunctionPhase) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    gain_calculator.o(i.GainCalculator_ValidateGain) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    gain_calculator.o(i.GainCalculator_ValidateGain) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    gain_calculator.o(i.ValidateGainConfig) refers to gain_calculator.o(i.GainCalculator_ValidateGain) for GainCalculator_ValidateGain
    signal_generator.o(i.SignalGenerator_ClearInputBuffer) refers to rt_memclr.o(.text) for __aeabi_memclr
    signal_generator.o(i.SignalGenerator_ClearInputBuffer) refers to signal_generator.o(.data) for s_sig_gen
    signal_generator.o(i.SignalGenerator_DeInit) refers to matrix_keypad.o(i.MatrixKeypad_DeInit) for MatrixKeypad_DeInit
    signal_generator.o(i.SignalGenerator_DeInit) refers to signal_generator.o(.data) for g_signal_generator_initialized
    signal_generator.o(i.SignalGenerator_DisplayAmpSet) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    signal_generator.o(i.SignalGenerator_DisplayAmpSet) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    signal_generator.o(i.SignalGenerator_DisplayAmpSet) refers to _printf_str.o(.text) for _printf_str
    signal_generator.o(i.SignalGenerator_DisplayAmpSet) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    signal_generator.o(i.SignalGenerator_DisplayAmpSet) refers to _printf_dec.o(.text) for _printf_int_dec
    signal_generator.o(i.SignalGenerator_DisplayAmpSet) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    signal_generator.o(i.SignalGenerator_DisplayAmpSet) refers to __2sprintf.o(.text) for __2sprintf
    signal_generator.o(i.SignalGenerator_DisplayAmpSet) refers to signal_generator.o(.data) for s_sig_gen
    signal_generator.o(i.SignalGenerator_DisplayFreqSet) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    signal_generator.o(i.SignalGenerator_DisplayFreqSet) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    signal_generator.o(i.SignalGenerator_DisplayFreqSet) refers to _printf_str.o(.text) for _printf_str
    signal_generator.o(i.SignalGenerator_DisplayFreqSet) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    signal_generator.o(i.SignalGenerator_DisplayFreqSet) refers to _printf_dec.o(.text) for _printf_int_dec
    signal_generator.o(i.SignalGenerator_DisplayFreqSet) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    signal_generator.o(i.SignalGenerator_DisplayFreqSet) refers to __2sprintf.o(.text) for __2sprintf
    signal_generator.o(i.SignalGenerator_DisplayFreqSet) refers to signal_generator.o(.data) for s_sig_gen
    signal_generator.o(i.SignalGenerator_DisplayMain) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    signal_generator.o(i.SignalGenerator_DisplayMain) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    signal_generator.o(i.SignalGenerator_DisplayMain) refers to _printf_dec.o(.text) for _printf_int_dec
    signal_generator.o(i.SignalGenerator_DisplayMain) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    signal_generator.o(i.SignalGenerator_DisplayMain) refers to _printf_str.o(.text) for _printf_str
    signal_generator.o(i.SignalGenerator_DisplayMain) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    signal_generator.o(i.SignalGenerator_DisplayMain) refers to __2sprintf.o(.text) for __2sprintf
    signal_generator.o(i.SignalGenerator_DisplayMain) refers to signal_generator.o(.data) for s_sig_gen
    signal_generator.o(i.SignalGenerator_DisplayMeasurement) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    signal_generator.o(i.SignalGenerator_DisplayMeasurement) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    signal_generator.o(i.SignalGenerator_DisplayMeasurement) refers to _printf_dec.o(.text) for _printf_int_dec
    signal_generator.o(i.SignalGenerator_DisplayMeasurement) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    signal_generator.o(i.SignalGenerator_DisplayMeasurement) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    signal_generator.o(i.SignalGenerator_DisplayMeasurement) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    signal_generator.o(i.SignalGenerator_DisplayMeasurement) refers to __2sprintf.o(.text) for __2sprintf
    signal_generator.o(i.SignalGenerator_DisplayMeasurement) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    signal_generator.o(i.SignalGenerator_DisplayMeasurement) refers to signal_generator.o(.bss) for s_measurement
    signal_generator.o(i.SignalGenerator_DisplayWaveSet) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    signal_generator.o(i.SignalGenerator_DisplayWaveSet) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    signal_generator.o(i.SignalGenerator_DisplayWaveSet) refers to _printf_str.o(.text) for _printf_str
    signal_generator.o(i.SignalGenerator_DisplayWaveSet) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    signal_generator.o(i.SignalGenerator_DisplayWaveSet) refers to __2sprintf.o(.text) for __2sprintf
    signal_generator.o(i.SignalGenerator_DisplayWaveSet) refers to signal_generator.o(.data) for s_sig_gen
    signal_generator.o(i.SignalGenerator_GetMeasurement) refers to signal_generator.o(.bss) for s_measurement
    signal_generator.o(i.SignalGenerator_GetParams) refers to signal_generator.o(.data) for s_sig_gen
    signal_generator.o(i.SignalGenerator_Init) refers to matrix_keypad.o(i.MatrixKeypad_Init) for MatrixKeypad_Init
    signal_generator.o(i.SignalGenerator_Init) refers to ad9910_control.o(i.AD9910_Control_Init) for AD9910_Control_Init
    signal_generator.o(i.SignalGenerator_Init) refers to signal_generator.o(i.SignalGenerator_UpdateParameters) for SignalGenerator_UpdateParameters
    signal_generator.o(i.SignalGenerator_Init) refers to signal_generator.o(.data) for g_signal_generator_initialized
    signal_generator.o(i.SignalGenerator_IsReady) refers to signal_generator.o(.data) for g_signal_generator_initialized
    signal_generator.o(i.SignalGenerator_ParseAmplitude) refers to strlen.o(.text) for strlen
    signal_generator.o(i.SignalGenerator_ParseAmplitude) refers to atoi.o(.text) for atoi
    signal_generator.o(i.SignalGenerator_ParseAmplitude) refers to signal_generator.o(.data) for s_sig_gen
    signal_generator.o(i.SignalGenerator_ParseFrequency) refers to strlen.o(.text) for strlen
    signal_generator.o(i.SignalGenerator_ParseFrequency) refers to atol.o(.text) for atol
    signal_generator.o(i.SignalGenerator_ParseFrequency) refers to signal_generator.o(.data) for s_sig_gen
    signal_generator.o(i.SignalGenerator_Process) refers to systick.o(i.SysTick_GetTick) for SysTick_GetTick
    signal_generator.o(i.SignalGenerator_Process) refers to matrix_keypad.o(i.MatrixKeypad_ScanAll) for MatrixKeypad_ScanAll
    signal_generator.o(i.SignalGenerator_Process) refers to signal_generator.o(i.SignalGenerator_ProcessKeypad1) for SignalGenerator_ProcessKeypad1
    signal_generator.o(i.SignalGenerator_Process) refers to signal_generator.o(i.SignalGenerator_ProcessKeypad2) for SignalGenerator_ProcessKeypad2
    signal_generator.o(i.SignalGenerator_Process) refers to signal_generator.o(i.SignalGenerator_UpdateDisplay) for SignalGenerator_UpdateDisplay
    signal_generator.o(i.SignalGenerator_Process) refers to signal_generator.o(i.SignalGenerator_UpdateParameters) for SignalGenerator_UpdateParameters
    signal_generator.o(i.SignalGenerator_Process) refers to signal_generator.o(.data) for g_signal_generator_initialized
    signal_generator.o(i.SignalGenerator_ProcessDigitInput) refers to signal_generator.o(.data) for s_sig_gen
    signal_generator.o(i.SignalGenerator_ProcessKeypad1) refers to signal_generator.o(i.SignalGenerator_ProcessDigitInput) for SignalGenerator_ProcessDigitInput
    signal_generator.o(i.SignalGenerator_ProcessKeypad1) refers to signal_generator.o(i.SignalGenerator_ClearInputBuffer) for SignalGenerator_ClearInputBuffer
    signal_generator.o(i.SignalGenerator_ProcessKeypad1) refers to signal_generator.o(i.SignalGenerator_ParseFrequency) for SignalGenerator_ParseFrequency
    signal_generator.o(i.SignalGenerator_ProcessKeypad1) refers to signal_generator.o(i.SignalGenerator_SetFrequency) for SignalGenerator_SetFrequency
    signal_generator.o(i.SignalGenerator_ProcessKeypad1) refers to signal_generator.o(i.SignalGenerator_ParseAmplitude) for SignalGenerator_ParseAmplitude
    signal_generator.o(i.SignalGenerator_ProcessKeypad1) refers to signal_generator.o(i.SignalGenerator_SetAmplitude) for SignalGenerator_SetAmplitude
    signal_generator.o(i.SignalGenerator_ProcessKeypad1) refers to signal_generator.o(.data) for s_sig_gen
    signal_generator.o(i.SignalGenerator_ProcessKeypad2) refers to signal_generator.o(i.SignalGenerator_ClearInputBuffer) for SignalGenerator_ClearInputBuffer
    signal_generator.o(i.SignalGenerator_ProcessKeypad2) refers to signal_generator.o(i.SignalGenerator_SetWaveType) for SignalGenerator_SetWaveType
    signal_generator.o(i.SignalGenerator_ProcessKeypad2) refers to signal_generator.o(.data) for s_sig_gen
    signal_generator.o(i.SignalGenerator_SetAmplitude) refers to signal_generator.o(.data) for s_sig_gen
    signal_generator.o(i.SignalGenerator_SetFrequency) refers to signal_generator.o(.data) for s_sig_gen
    signal_generator.o(i.SignalGenerator_SetWaveType) refers to signal_generator.o(.data) for s_sig_gen
    signal_generator.o(i.SignalGenerator_StartMeasurement) refers to signal_generator.o(.data) for s_sig_gen
    signal_generator.o(i.SignalGenerator_StartMeasurement) refers to signal_generator.o(.bss) for s_measurement
    signal_generator.o(i.SignalGenerator_UpdateDisplay) refers to oled.o(i.OLED_Clear) for OLED_Clear
    signal_generator.o(i.SignalGenerator_UpdateDisplay) refers to signal_generator.o(i.SignalGenerator_DisplayMain) for SignalGenerator_DisplayMain
    signal_generator.o(i.SignalGenerator_UpdateDisplay) refers to signal_generator.o(i.SignalGenerator_DisplayFreqSet) for SignalGenerator_DisplayFreqSet
    signal_generator.o(i.SignalGenerator_UpdateDisplay) refers to signal_generator.o(i.SignalGenerator_DisplayAmpSet) for SignalGenerator_DisplayAmpSet
    signal_generator.o(i.SignalGenerator_UpdateDisplay) refers to signal_generator.o(i.SignalGenerator_DisplayWaveSet) for SignalGenerator_DisplayWaveSet
    signal_generator.o(i.SignalGenerator_UpdateDisplay) refers to signal_generator.o(i.SignalGenerator_DisplayMeasurement) for SignalGenerator_DisplayMeasurement
    signal_generator.o(i.SignalGenerator_UpdateDisplay) refers to oled.o(i.OLED_Refresh_Gram) for OLED_Refresh_Gram
    signal_generator.o(i.SignalGenerator_UpdateDisplay) refers to signal_generator.o(.data) for g_signal_generator_initialized
    signal_generator.o(i.SignalGenerator_UpdateParameters) refers to ad9910_control.o(i.AD9910_Control_SetFrequency) for AD9910_Control_SetFrequency
    signal_generator.o(i.SignalGenerator_UpdateParameters) refers to ad9910_control.o(i.AD9910_Control_SetTargetAmplitude) for AD9910_Control_SetTargetAmplitude
    signal_generator.o(i.SignalGenerator_UpdateParameters) refers to signal_generator.o(.data) for s_sig_gen
    signal_generator.o(.data) refers to signal_generator.o(.conststring) for .conststring
    matrix_keypad.o(i.MatrixKeypad_DeInit) refers to matrix_keypad.o(.data) for g_keypad_initialized
    matrix_keypad.o(i.MatrixKeypad_GPIO_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    matrix_keypad.o(i.MatrixKeypad_GPIO_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    matrix_keypad.o(i.MatrixKeypad_GPIO_Init) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    matrix_keypad.o(i.MatrixKeypad_GetKeyName) refers to matrix_keypad.o(.data) for s_keypad1_names
    matrix_keypad.o(i.MatrixKeypad_Init) refers to matrix_keypad.o(i.MatrixKeypad_GPIO_Init) for MatrixKeypad_GPIO_Init
    matrix_keypad.o(i.MatrixKeypad_Init) refers to matrix_keypad.o(.data) for g_keypad_initialized
    matrix_keypad.o(i.MatrixKeypad_IsReady) refers to matrix_keypad.o(.data) for g_keypad_initialized
    matrix_keypad.o(i.MatrixKeypad_ReadColumn) refers to stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    matrix_keypad.o(i.MatrixKeypad_ReadColumn) refers to matrix_keypad.o(.constdata) for s_keypad_configs
    matrix_keypad.o(i.MatrixKeypad_Scan) refers to matrix_keypad.o(i.MatrixKeypad_ScanSingle) for MatrixKeypad_ScanSingle
    matrix_keypad.o(i.MatrixKeypad_Scan) refers to matrix_keypad.o(.data) for g_keypad_initialized
    matrix_keypad.o(i.MatrixKeypad_ScanAll) refers to matrix_keypad.o(i.MatrixKeypad_ScanSingle) for MatrixKeypad_ScanSingle
    matrix_keypad.o(i.MatrixKeypad_ScanAll) refers to systick.o(i.SysTick_GetTick) for SysTick_GetTick
    matrix_keypad.o(i.MatrixKeypad_ScanAll) refers to matrix_keypad.o(.data) for g_keypad_initialized
    matrix_keypad.o(i.MatrixKeypad_ScanSingle) refers to matrix_keypad.o(i.MatrixKeypad_SetRowOutput) for MatrixKeypad_SetRowOutput
    matrix_keypad.o(i.MatrixKeypad_ScanSingle) refers to matrix_keypad.o(i.MatrixKeypad_ReadColumn) for MatrixKeypad_ReadColumn
    matrix_keypad.o(i.MatrixKeypad_SetRowOutput) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    matrix_keypad.o(i.MatrixKeypad_SetRowOutput) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    matrix_keypad.o(i.MatrixKeypad_SetRowOutput) refers to matrix_keypad.o(.constdata) for s_keypad_configs
    matrix_keypad.o(.data) refers to matrix_keypad.o(.conststring) for .conststring
    key.o(i.Key_DeInit) refers to key.o(.data) for g_key_initialized
    key.o(i.Key_GPIO_Config) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    key.o(i.Key_GPIO_Config) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    key.o(i.Key_GetState) refers to key.o(.data) for g_key_initialized
    key.o(i.Key_Init) refers to key.o(i.Key_GPIO_Config) for Key_GPIO_Config
    key.o(i.Key_Init) refers to systick.o(i.SysTick_GetTick) for SysTick_GetTick
    key.o(i.Key_Init) refers to key.o(.data) for s_key_states
    key.o(i.Key_Init) refers to key.o(.bss) for s_key_debounce_time
    key.o(i.Key_IsPressed) refers to key.o(i.Key_GetState) for Key_GetState
    key.o(i.Key_IsReady) refers to key.o(.data) for g_key_initialized
    key.o(i.Key_ReadRaw) refers to stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    key.o(i.Key_Scan) refers to systick.o(i.SysTick_GetTick) for SysTick_GetTick
    key.o(i.Key_Scan) refers to key.o(i.Key_ReadRaw) for Key_ReadRaw
    key.o(i.Key_Scan) refers to key.o(.data) for g_key_initialized
    key.o(i.Key_Scan) refers to key.o(.bss) for s_key_debounce_time
    key.o(i.Key_WaitPress) refers to systick.o(i.SysTick_GetTick) for SysTick_GetTick
    key.o(i.Key_WaitPress) refers to key.o(i.Key_Scan) for Key_Scan
    key.o(i.Key_WaitPress) refers to systick.o(i.Delay_ms) for Delay_ms
    key.o(i.Key_WaitPress) refers to key.o(.data) for g_key_initialized
    key.o(i.Key_WaitRelease) refers to systick.o(i.SysTick_GetTick) for SysTick_GetTick
    key.o(i.Key_WaitRelease) refers to key.o(i.Key_Scan) for Key_Scan
    key.o(i.Key_WaitRelease) refers to systick.o(i.Delay_ms) for Delay_ms
    key.o(i.Key_WaitRelease) refers to key.o(i.Key_IsPressed) for Key_IsPressed
    key.o(i.Key_WaitRelease) refers to key.o(.data) for g_key_initialized
    oled.o(i.IIC_Send_Byte) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    oled.o(i.IIC_Send_Byte) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.IIC_Send_Byte) refers to systick.o(i.Delay_us) for Delay_us
    oled.o(i.IIC_Start) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.IIC_Start) refers to systick.o(i.Delay_us) for Delay_us
    oled.o(i.IIC_Start) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    oled.o(i.IIC_Stop) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    oled.o(i.IIC_Stop) refers to systick.o(i.Delay_us) for Delay_us
    oled.o(i.IIC_Stop) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.IIC_Wait_Ack) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.IIC_Wait_Ack) refers to systick.o(i.Delay_us) for Delay_us
    oled.o(i.IIC_Wait_Ack) refers to oled.o(i.IIC_Stop) for IIC_Stop
    oled.o(i.IIC_Wait_Ack) refers to stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    oled.o(i.IIC_Wait_Ack) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Refresh_Gram) for OLED_Refresh_Gram
    oled.o(i.OLED_Clear) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_Diagnostic_Test) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.OLED_Diagnostic_Test) refers to systick.o(i.Delay_ms) for Delay_ms
    oled.o(i.OLED_Diagnostic_Test) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    oled.o(i.OLED_Diagnostic_Test) refers to oled.o(i.IIC_Start) for IIC_Start
    oled.o(i.OLED_Diagnostic_Test) refers to oled.o(i.IIC_Send_Byte) for IIC_Send_Byte
    oled.o(i.OLED_Diagnostic_Test) refers to oled.o(i.IIC_Wait_Ack) for IIC_Wait_Ack
    oled.o(i.OLED_Diagnostic_Test) refers to oled.o(i.IIC_Stop) for IIC_Stop
    oled.o(i.OLED_Diagnostic_Test) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Display_Off) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Display_On) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DrawBMP) refers to oled.o(i.OLED_Set_Pos) for OLED_Set_Pos
    oled.o(i.OLED_DrawBMP) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DrawPoint) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_Fill) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_Fill) refers to oled.o(i.OLED_Refresh_Gram) for OLED_Refresh_Gram
    oled.o(i.OLED_Force_Clear) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Force_Clear) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    oled.o(i.OLED_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    oled.o(i.OLED_Init) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.OLED_Init) refers to systick.o(i.Delay_ms) for Delay_ms
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Set_Pos) for OLED_Set_Pos
    oled.o(i.OLED_Refresh_Gram) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Refresh_Gram) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_Set_Pos) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_ShowCHinese) refers to oled.o(i.OLED_Set_Pos) for OLED_Set_Pos
    oled.o(i.OLED_ShowCHinese) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_ShowChar) refers to oled_font.o(i.OLED_GetCharFont) for OLED_GetCharFont
    oled.o(i.OLED_ShowChar) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_ShowNum) refers to oled.o(i.mypow) for mypow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_Simple_Test) refers to oled.o(i.OLED_Force_Clear) for OLED_Force_Clear
    oled.o(i.OLED_Simple_Test) refers to oled.o(i.OLED_Refresh_Gram) for OLED_Refresh_Gram
    oled.o(i.OLED_Simple_Test) refers to systick.o(i.Delay_ms) for Delay_ms
    oled.o(i.OLED_Simple_Test) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_Simple_Test) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_Test_Display) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Test_Display) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_Test_Display) refers to oled.o(i.OLED_Refresh_Gram) for OLED_Refresh_Gram
    oled.o(i.OLED_Test_Display) refers to systick.o(i.Delay_ms) for Delay_ms
    oled.o(i.OLED_Test_Display) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.IIC_Start) for IIC_Start
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.IIC_Send_Byte) for IIC_Send_Byte
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.IIC_Wait_Ack) for IIC_Wait_Ack
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.IIC_Stop) for IIC_Stop
    oled.o(i.fill_picture) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled_font.o(i.OLED_GetCharFont) refers to oled_font.o(.constdata) for F8X16
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __2snprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2snprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __2snprintf.o(.text) refers to _snputc.o(.text) for _snputc
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2snprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2snprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2snprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    atoi.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    atol.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    atol.o(.text) refers to strtol.o(.text) for strtol
    strtoul.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    strtoul.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    strtoul.o(.text) refers to _strtoul.o(.text) for _strtoul
    rt_memmove_v6.o(.text) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    rt_memmove_v6.o(.text) refers to rt_memmove_w.o(.text) for __memmove_aligned
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    basic.o(x$fpl$basic) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    atan2.o(i.__hardfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.__hardfp_atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.__hardfp_atan2) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan2.o(i.__hardfp_atan2) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2.o(i.__hardfp_atan2) refers to fabs.o(i.fabs) for fabs
    atan2.o(i.__hardfp_atan2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2.o(i.__hardfp_atan2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2.o(i.__hardfp_atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2.o(i.__softfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2.o(i.atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.____hardfp_atan2$lsc) refers to _rserrno.o(.text) for __set_errno
    atan2_x.o(i.____hardfp_atan2$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan2_x.o(i.____hardfp_atan2$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2_x.o(i.____hardfp_atan2$lsc) refers to fabs.o(i.fabs) for fabs
    atan2_x.o(i.____hardfp_atan2$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2_x.o(i.____hardfp_atan2$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2_x.o(i.____hardfp_atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    fabs.o(i.__hardfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log10.o(i.__hardfp_log10) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log10.o(i.__hardfp_log10) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    log10.o(i.__hardfp_log10) refers to _rserrno.o(.text) for __set_errno
    log10.o(i.__hardfp_log10) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    log10.o(i.__hardfp_log10) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    log10.o(i.__hardfp_log10) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    log10.o(i.__hardfp_log10) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    log10.o(i.__hardfp_log10) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    log10.o(i.__hardfp_log10) refers to log.o(i.log) for log
    log10.o(i.__hardfp_log10) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    log10.o(i.__softfp_log10) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log10.o(i.__softfp_log10) refers to log10.o(i.__hardfp_log10) for __hardfp_log10
    log10.o(i.log10) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log10.o(i.log10) refers to log10.o(i.__hardfp_log10) for __hardfp_log10
    log10_x.o(i.____hardfp_log10$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log10_x.o(i.____hardfp_log10$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    log10_x.o(i.____hardfp_log10$lsc) refers to _rserrno.o(.text) for __set_errno
    log10_x.o(i.____hardfp_log10$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    log10_x.o(i.____hardfp_log10$lsc) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    log10_x.o(i.____hardfp_log10$lsc) refers to log.o(i.log) for log
    log10_x.o(i.____hardfp_log10$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    log10_x.o(i.____softfp_log10$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log10_x.o(i.____softfp_log10$lsc) refers to log10_x.o(i.____hardfp_log10$lsc) for ____hardfp_log10$lsc
    log10_x.o(i.__log10$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log10_x.o(i.__log10$lsc) refers to log10_x.o(i.____hardfp_log10$lsc) for ____hardfp_log10$lsc
    sqrt.o(i.__hardfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    strtod.o(i.__hardfp_strtod) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    strtod.o(i.__hardfp_strtod) refers to strtod.o(.text) for __strtod_int
    strtod.o(i.__softfp_strtod) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    strtod.o(i.__softfp_strtod) refers to strtod.o(.text) for __strtod_int
    strtod.o(i.strtod) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    strtod.o(i.strtod) refers to strtod.o(.text) for __strtod_int
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_char_common.o(.text) refers to __printf_ss_wp.o(.text) for __printf
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    strtod.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    strtod.o(.text) refers to scanf1.o(x$fpl$scanf1) for _scanf_real
    strtod.o(.text) refers to _sgetc.o(.text) for _sgetc
    strtod.o(.text) refers to isspace.o(.text) for isspace
    strtol.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    rt_memmove_w.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__hardfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.__hardfp_atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.__hardfp_atan) refers to fabs.o(i.fabs) for fabs
    atan.o(i.__hardfp_atan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan.o(i.__hardfp_atan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan.o(i.__hardfp_atan) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan.o(i.__hardfp_atan) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan.o(i.__hardfp_atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.__hardfp_atan) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan.o(i.__hardfp_atan) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan.o(i.__hardfp_atan) refers to atan.o(.constdata) for .constdata
    atan.o(i.__softfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(i.atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.____hardfp_atan$lsc) refers to fabs.o(i.fabs) for fabs
    atan_x.o(i.____hardfp_atan$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan_x.o(i.____hardfp_atan$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan_x.o(i.____hardfp_atan$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan_x.o(i.____hardfp_atan$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan_x.o(i.____hardfp_atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.____hardfp_atan$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan_x.o(i.____hardfp_atan$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan_x.o(i.____hardfp_atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    log.o(i.__hardfp_log) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log.o(i.__hardfp_log) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    log.o(i.__hardfp_log) refers to _rserrno.o(.text) for __set_errno
    log.o(i.__hardfp_log) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    log.o(i.__hardfp_log) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    log.o(i.__hardfp_log) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    log.o(i.__hardfp_log) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    log.o(i.__hardfp_log) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    log.o(i.__hardfp_log) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    log.o(i.__hardfp_log) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    log.o(i.__hardfp_log) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    log.o(i.__hardfp_log) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    log.o(i.__hardfp_log) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    log.o(i.__hardfp_log) refers to poly.o(i.__kernel_poly) for __kernel_poly
    log.o(i.__hardfp_log) refers to qnan.o(.constdata) for __mathlib_zero
    log.o(i.__hardfp_log) refers to log.o(.constdata) for .constdata
    log.o(i.__softfp_log) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log.o(i.__softfp_log) refers to log.o(i.__hardfp_log) for __hardfp_log
    log.o(i.log) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log.o(i.log) refers to log.o(i.__hardfp_log) for __hardfp_log
    log.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log_x.o(i.____hardfp_log$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log_x.o(i.____hardfp_log$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    log_x.o(i.____hardfp_log$lsc) refers to _rserrno.o(.text) for __set_errno
    log_x.o(i.____hardfp_log$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    log_x.o(i.____hardfp_log$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    log_x.o(i.____hardfp_log$lsc) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    log_x.o(i.____hardfp_log$lsc) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    log_x.o(i.____hardfp_log$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    log_x.o(i.____hardfp_log$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    log_x.o(i.____hardfp_log$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    log_x.o(i.____hardfp_log$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    log_x.o(i.____hardfp_log$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    log_x.o(i.____hardfp_log$lsc) refers to log_x.o(.constdata) for .constdata
    log_x.o(i.____softfp_log$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log_x.o(i.____softfp_log$lsc) refers to log_x.o(i.____hardfp_log$lsc) for ____hardfp_log$lsc
    log_x.o(i.__log$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log_x.o(i.__log$lsc) refers to log_x.o(i.____hardfp_log$lsc) for ____hardfp_log$lsc
    log_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    isspace.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    deqf.o(x$fpl$deqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf1.o(x$fpl$scanf1) refers to scanf_fp.o(.text) for _scanf_really_real
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f40_41xxx.o(.text) for __user_initial_stackheap
    scanf_fp.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf_fp.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    scanf_fp.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    scanf_fp.o(.text) refers to istatus.o(x$fpl$ieeestatus) for __ieee_status
    scanf_fp.o(.text) refers to bigflt0.o(.text) for _btod_etento
    scanf_fp.o(.text) refers to btod.o(CL$$btod_emuld) for _btod_emuld
    scanf_fp.o(.text) refers to btod.o(CL$$btod_edivd) for _btod_edivd
    scanf_fp.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    scanf_fp.o(.text) refers to scanf2.o(x$fpl$scanf2) for _scanf_infnan
    scanf_fp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to fpconst.o(c$$dmax) for __dbl_max
    scanf_fp.o(.text) refers to fpconst.o(c$$dinf) for __huge_val
    scanf_fp.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    fpconst.o(c$$dinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$dnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$finf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$dmax) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf2.o(x$fpl$scanf2) refers to scanf_hexfp.o(.text) for _scanf_really_hex_real
    scanf2.o(x$fpl$scanf2) refers to scanf_infnan.o(.text) for _scanf_really_infnan
    scanf2b.o(x$fpl$scanf2) refers to scanf_hexfp.o(.text) for _scanf_really_hex_real
    scanf2b.o(x$fpl$scanf2) refers to scanf_infnan.o(.text) for _scanf_really_infnan
    narrow.o(i.__hardfp___mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__hardfp___mathlib_tofloat) refers to frexp.o(i.frexp) for frexp
    narrow.o(i.__hardfp___mathlib_tofloat) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    narrow.o(i.__hardfp___mathlib_tofloat) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    narrow.o(i.__hardfp___mathlib_tofloat) refers to _rserrno.o(.text) for __set_errno
    narrow.o(i.__hardfp___mathlib_tofloat) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    narrow.o(i.__mathlib_narrow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_narrow) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    narrow.o(i.__mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_tofloat) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    narrow.o(i.__softfp___mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__softfp___mathlib_tofloat) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    scanf_hexfp.o(.text) refers to _chval.o(.text) for _chval
    scanf_hexfp.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    scanf_hexfp.o(.text) refers to ldexp.o(i.__support_ldexp) for __support_ldexp
    scanf_hexfp.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    frexp.o(i.__hardfp_frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.__hardfp_frexp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    frexp.o(i.__softfp_frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.__softfp_frexp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    frexp.o(i.frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.frexp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__hardfp_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__hardfp_ldexp) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    ldexp.o(i.__hardfp_ldexp) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp.o(i.__hardfp_ldexp) refers to _rserrno.o(.text) for __set_errno
    ldexp.o(i.__hardfp_ldexp) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    ldexp.o(i.__hardfp_ldexp) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    ldexp.o(i.__softfp_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__softfp_ldexp) refers to ldexp.o(i.__hardfp_ldexp) for __hardfp_ldexp
    ldexp.o(i.__support_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__support_ldexp) refers to ldexp.o(i.__hardfp_ldexp) for __hardfp_ldexp
    ldexp.o(i.ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.ldexp) refers to ldexp.o(i.__hardfp_ldexp) for __hardfp_ldexp
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to _rserrno.o(.text) for __set_errno
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    ldexp_x.o(i.____softfp_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____softfp_ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    ldexp_x.o(i.____support_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____support_ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    ldexp_x.o(i.__ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.__ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (192 bytes).
    Removing bsp.o(.rev16_text), (4 bytes).
    Removing bsp.o(.revsh_text), (4 bytes).
    Removing bsp.o(.rrx_text), (6 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing misc.o(.rrx_text), (6 bytes).
    Removing misc.o(i.NVIC_Init), (192 bytes).
    Removing misc.o(i.NVIC_PriorityGroupConfig), (80 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (88 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (92 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (80 bytes).
    Removing stm32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogCmd), (152 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (172 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (116 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AutoInjectedConvCmd), (112 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ClearFlag), (100 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ClearITPendingBit), (124 bytes).
    Removing stm32f4xx_adc.o(i.ADC_Cmd), (112 bytes).
    Removing stm32f4xx_adc.o(i.ADC_CommonInit), (380 bytes).
    Removing stm32f4xx_adc.o(i.ADC_CommonStructInit), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ContinuousModeCmd), (112 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DMACmd), (112 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd), (112 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DeInit), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DiscModeChannelCountConfig), (116 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DiscModeCmd), (112 bytes).
    Removing stm32f4xx_adc.o(i.ADC_EOCOnEachRegularChannelCmd), (112 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (192 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvEdgeConfig), (120 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetConversionValue), (76 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetFlagStatus), (128 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetITStatus), (160 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetInjectedConversionValue), (124 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetMultiModeConversionValue), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetSoftwareStartConvStatus), (92 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (92 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ITConfig), (168 bytes).
    Removing stm32f4xx_adc.o(i.ADC_Init), (448 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedChannelConfig), (368 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedDiscModeCmd), (112 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedSequencerLengthConfig), (116 bytes).
    Removing stm32f4xx_adc.o(i.ADC_MultiModeDMARequestAfterLastTransferCmd), (88 bytes).
    Removing stm32f4xx_adc.o(i.ADC_RegularChannelConfig), (444 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SetInjectedOffset), (140 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SoftwareStartConv), (80 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SoftwareStartInjectedConv), (80 bytes).
    Removing stm32f4xx_adc.o(i.ADC_StructInit), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_TempSensorVrefintCmd), (88 bytes).
    Removing stm32f4xx_adc.o(i.ADC_VBATCmd), (88 bytes).
    Removing stm32f4xx_can.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_can.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_can.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_can.o(i.CAN_CancelTransmit), (120 bytes).
    Removing stm32f4xx_can.o(i.CAN_ClearFlag), (220 bytes).
    Removing stm32f4xx_can.o(i.CAN_ClearITPendingBit), (304 bytes).
    Removing stm32f4xx_can.o(i.CAN_DBGFreeze), (104 bytes).
    Removing stm32f4xx_can.o(i.CAN_DeInit), (104 bytes).
    Removing stm32f4xx_can.o(i.CAN_FIFORelease), (96 bytes).
    Removing stm32f4xx_can.o(i.CAN_FilterInit), (396 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetFlagStatus), (328 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetITStatus), (428 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetLSBTransmitErrorCounter), (72 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetLastErrorCode), (72 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetReceiveErrorCounter), (72 bytes).
    Removing stm32f4xx_can.o(i.CAN_ITConfig), (184 bytes).
    Removing stm32f4xx_can.o(i.CAN_Init), (580 bytes).
    Removing stm32f4xx_can.o(i.CAN_MessagePending), (112 bytes).
    Removing stm32f4xx_can.o(i.CAN_OperatingModeRequest), (248 bytes).
    Removing stm32f4xx_can.o(i.CAN_Receive), (312 bytes).
    Removing stm32f4xx_can.o(i.CAN_SlaveStartBank), (100 bytes).
    Removing stm32f4xx_can.o(i.CAN_Sleep), (92 bytes).
    Removing stm32f4xx_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f4xx_can.o(i.CAN_TTComModeCmd), (184 bytes).
    Removing stm32f4xx_can.o(i.CAN_Transmit), (464 bytes).
    Removing stm32f4xx_can.o(i.CAN_TransmitStatus), (232 bytes).
    Removing stm32f4xx_can.o(i.CAN_WakeUp), (108 bytes).
    Removing stm32f4xx_can.o(i.CheckITStatus), (18 bytes).
    Removing stm32f4xx_cec.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cec.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cec.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_crc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_crc.o(i.CRC_CalcBlockCRC), (36 bytes).
    Removing stm32f4xx_crc.o(i.CRC_CalcCRC), (16 bytes).
    Removing stm32f4xx_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f4xx_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f4xx_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f4xx_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f4xx_cryp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_Cmd), (84 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_DMACmd), (108 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_DataIn), (12 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_DataOut), (12 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_DeInit), (20 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_FIFOFlush), (20 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_GetCmdStatus), (24 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_GetFlagStatus), (112 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_GetITStatus), (76 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_ITConfig), (108 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_IVInit), (24 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_IVStructInit), (12 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_Init), (312 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_KeyInit), (40 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_KeyStructInit), (20 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_PhaseConfig), (84 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_RestoreContext), (148 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_SaveContext), (264 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_StructInit), (12 bytes).
    Removing stm32f4xx_cryp_aes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC), (540 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM), (1778 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR), (466 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB), (494 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM), (1308 bytes).
    Removing stm32f4xx_cryp_des.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_cryp_des.o(i.CRYP_DES_CBC), (250 bytes).
    Removing stm32f4xx_cryp_des.o(i.CRYP_DES_ECB), (226 bytes).
    Removing stm32f4xx_cryp_tdes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC), (282 bytes).
    Removing stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB), (258 bytes).
    Removing stm32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dac.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dac.o(i.DAC_ClearFlag), (80 bytes).
    Removing stm32f4xx_dac.o(i.DAC_ClearITPendingBit), (80 bytes).
    Removing stm32f4xx_dac.o(i.DAC_Cmd), (104 bytes).
    Removing stm32f4xx_dac.o(i.DAC_DMACmd), (108 bytes).
    Removing stm32f4xx_dac.o(i.DAC_DeInit), (22 bytes).
    Removing stm32f4xx_dac.o(i.DAC_DualSoftwareTriggerCmd), (80 bytes).
    Removing stm32f4xx_dac.o(i.DAC_GetDataOutputValue), (80 bytes).
    Removing stm32f4xx_dac.o(i.DAC_GetFlagStatus), (96 bytes).
    Removing stm32f4xx_dac.o(i.DAC_GetITStatus), (116 bytes).
    Removing stm32f4xx_dac.o(i.DAC_ITConfig), (124 bytes).
    Removing stm32f4xx_dac.o(i.DAC_Init), (388 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SetChannel1Data), (100 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SetChannel2Data), (100 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SetDualChannelData), (136 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SoftwareTriggerCmd), (108 bytes).
    Removing stm32f4xx_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f4xx_dac.o(i.DAC_WaveGenerationCmd), (128 bytes).
    Removing stm32f4xx_dbgmcu.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_APB1PeriphConfig), (104 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_APB2PeriphConfig), (104 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_Config), (100 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_GetREVID), (12 bytes).
    Removing stm32f4xx_dcmi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_CROPCmd), (84 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_CROPConfig), (32 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_CaptureCmd), (84 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_ClearFlag), (64 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_Cmd), (84 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_DeInit), (28 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_GetFlagStatus), (172 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_GetITStatus), (92 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_ITConfig), (108 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_Init), (264 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_JPEGCmd), (84 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_ReadData), (12 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_SetEmbeddedSynchroCodes), (32 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_StructInit), (18 bytes).
    Removing stm32f4xx_dfsdm.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dfsdm.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dfsdm.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dma.o(i.DMA_ClearFlag), (256 bytes).
    Removing stm32f4xx_dma.o(i.DMA_ClearITPendingBit), (256 bytes).
    Removing stm32f4xx_dma.o(i.DMA_Cmd), (216 bytes).
    Removing stm32f4xx_dma.o(i.DMA_DeInit), (508 bytes).
    Removing stm32f4xx_dma.o(i.DMA_DoubleBufferModeCmd), (216 bytes).
    Removing stm32f4xx_dma.o(i.DMA_DoubleBufferModeConfig), (220 bytes).
    Removing stm32f4xx_dma.o(i.DMA_FlowControllerConfig), (216 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCmdStatus), (192 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCurrDataCounter), (180 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCurrentMemoryTarget), (192 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetFIFOStatus), (184 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetFlagStatus), (580 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetITStatus), (684 bytes).
    Removing stm32f4xx_dma.o(i.DMA_ITConfig), (272 bytes).
    Removing stm32f4xx_dma.o(i.DMA_Init), (696 bytes).
    Removing stm32f4xx_dma.o(i.DMA_MemoryTargetConfig), (208 bytes).
    Removing stm32f4xx_dma.o(i.DMA_PeriphIncOffsetSizeConfig), (216 bytes).
    Removing stm32f4xx_dma.o(i.DMA_SetCurrDataCounter), (180 bytes).
    Removing stm32f4xx_dma.o(i.DMA_StructInit), (34 bytes).
    Removing stm32f4xx_dma2d.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_AbortTransfer), (20 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_BGConfig), (424 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_BGStart), (84 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_BG_StructInit), (26 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_ClearFlag), (80 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_ClearITPendingBit), (92 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_DeInit), (22 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_DeadTimeConfig), (120 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_FGConfig), (424 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_FGStart), (84 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_FG_StructInit), (26 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_GetFlagStatus), (92 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_GetITStatus), (124 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_ITConfig), (132 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_Init), (452 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_LineWatermarkConfig), (60 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_StartTransfer), (20 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_StructInit), (24 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_Suspend), (84 bytes).
    Removing stm32f4xx_dsi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dsi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dsi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_ClearFlag), (60 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_ClearITPendingBit), (64 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_GenerateSWInterrupt), (68 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_GetFlagStatus), (196 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_GetITStatus), (196 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_Init), (268 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f4xx_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ClearFlag), (68 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_DataCacheCmd), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_DataCacheReset), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllBank1Sectors), (164 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllBank2Sectors), (164 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllSectors), (164 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseSector), (300 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_GetFlagStatus), (104 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_GetStatus), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ITConfig), (104 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_InstructionCacheCmd), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_InstructionCacheReset), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_Lock), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_BORConfig), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_BootConfig), (76 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetBOR), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetPCROP), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetPCROP1), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetRDP), (24 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetWRP1), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Launch), (32 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROP1Config), (116 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROPConfig), (116 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROPSelectionConfig), (76 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_RDPConfig), (76 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Unlock), (36 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_UserConfig), (140 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_WRP1Config), (116 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_WRPConfig), (116 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_PrefetchBufferCmd), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramByte), (148 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramDoubleWord), (160 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramHalfWord), (152 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramWord), (152 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_Unlock), (36 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_WaitForLastOperation), (34 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_flash_ramfunc.o(i.FLASH_FlashInterfaceCmd), (36 bytes).
    Removing stm32f4xx_flash_ramfunc.o(i.FLASH_FlashSleepModeCmd), (36 bytes).
    Removing stm32f4xx_fmpi2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_fmpi2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_fmpi2c.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_fsmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_ClearFlag), (148 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_ClearITPendingBit), (152 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_GetECC), (28 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_GetFlagStatus), (148 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_GetITStatus), (164 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_ITConfig), (228 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDCmd), (164 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDDeInit), (120 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDECCCmd), (164 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDInit), (592 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NORSRAMCmd), (128 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NORSRAMDeInit), (112 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NORSRAMInit), (904 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NORSRAMStructInit), (52 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDCmd), (96 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDDeInit), (40 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDInit), (532 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f4xx_fsmc.o(.constdata), (28 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_DeInit), (416 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_PinAFConfig), (424 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_PinLockConfig), (204 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadInputData), (160 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadOutputData), (160 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadOutputDataBit), (268 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_StructInit), (18 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ToggleBits), (164 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_Write), (160 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_WriteBit), (280 bytes).
    Removing stm32f4xx_hash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hash.o(i.HASH_AutoStartDigest), (84 bytes).
    Removing stm32f4xx_hash.o(i.HASH_ClearFlag), (64 bytes).
    Removing stm32f4xx_hash.o(i.HASH_ClearITPendingBit), (64 bytes).
    Removing stm32f4xx_hash.o(i.HASH_DMACmd), (84 bytes).
    Removing stm32f4xx_hash.o(i.HASH_DataIn), (12 bytes).
    Removing stm32f4xx_hash.o(i.HASH_DeInit), (20 bytes).
    Removing stm32f4xx_hash.o(i.HASH_GetDigest), (72 bytes).
    Removing stm32f4xx_hash.o(i.HASH_GetFlagStatus), (108 bytes).
    Removing stm32f4xx_hash.o(i.HASH_GetITStatus), (84 bytes).
    Removing stm32f4xx_hash.o(i.HASH_GetInFIFOWordsNbr), (16 bytes).
    Removing stm32f4xx_hash.o(i.HASH_ITConfig), (104 bytes).
    Removing stm32f4xx_hash.o(i.HASH_Init), (232 bytes).
    Removing stm32f4xx_hash.o(i.HASH_Reset), (20 bytes).
    Removing stm32f4xx_hash.o(i.HASH_RestoreContext), (68 bytes).
    Removing stm32f4xx_hash.o(i.HASH_SaveContext), (60 bytes).
    Removing stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr), (76 bytes).
    Removing stm32f4xx_hash.o(i.HASH_StartDigest), (20 bytes).
    Removing stm32f4xx_hash.o(i.HASH_StructInit), (12 bytes).
    Removing stm32f4xx_hash_md5.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hash_md5.o(i.HASH_MD5), (180 bytes).
    Removing stm32f4xx_hash_md5.o(i.HMAC_MD5), (354 bytes).
    Removing stm32f4xx_hash_sha1.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hash_sha1.o(i.HASH_SHA1), (186 bytes).
    Removing stm32f4xx_hash_sha1.o(i.HMAC_SHA1), (362 bytes).
    Removing stm32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ARPCmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_AcknowledgeConfig), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_AnalogFilterCmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_CalculatePEC), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_CheckEvent), (316 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ClearFlag), (108 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ClearITPendingBit), (108 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_Cmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DMACmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DMALastTransferCmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DeInit), (148 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DigitalFilterConfig), (108 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DualAddressCmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_FastModeDutyCycleConfig), (128 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GeneralCallCmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GenerateSTART), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GenerateSTOP), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetFlagStatus), (300 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetITStatus), (244 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetLastEvent), (100 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetPEC), (76 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ITConfig), (132 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_Init), (440 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_NACKPositionConfig), (128 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_OwnAddress2Config), (92 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_PECPositionConfig), (128 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ReadRegister), (136 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ReceiveData), (76 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_SMBusAlertConfig), (128 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_Send7bitAddress), (108 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_SendData), (76 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_SoftwareResetCmd), (112 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_StretchClockCmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_StructInit), (30 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_TransmitPEC), (116 bytes).
    Removing stm32f4xx_iwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_GetFlagStatus), (76 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_SetPrescaler), (80 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_SetReload), (60 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_WriteAccessCmd), (64 bytes).
    Removing stm32f4xx_lptim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_lptim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_lptim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_ltdc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_CLUTCmd), (84 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_CLUTInit), (144 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_CLUTStructInit), (12 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ClearFlag), (72 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ClearITPendingBit), (64 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_Cmd), (84 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ColorKeyingConfig), (180 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ColorKeyingStructInit), (10 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_DeInit), (22 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_DitherCmd), (84 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetCDStatus), (84 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetFlagStatus), (84 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetITStatus), (96 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetPosStatus), (44 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetRGBWidth), (64 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ITConfig), (104 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_Init), (572 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LIPConfig), (60 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerAddress), (4 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerAlpha), (4 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerCmd), (76 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerInit), (540 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerPixelFormat), (106 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerPosition), (160 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerSize), (116 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerStructInit), (48 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_PosStructInit), (8 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_RGBStructInit), (10 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ReloadConfig), (64 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_StructInit), (34 bytes).
    Removing stm32f4xx_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_BackupAccessCmd), (56 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_BackupRegulatorCmd), (56 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_ClearFlag), (72 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_DeInit), (22 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_EnterSTANDBYMode), (40 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_EnterSTOPMode), (132 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_EnterUnderDriveSTOPMode), (140 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_FlashPowerDownCmd), (56 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_GetFlagStatus), (104 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_MainRegulatorModeConfig), (80 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_OverDriveCmd), (56 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_OverDriveSWCmd), (56 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_PVDCmd), (56 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_PVDLevelConfig), (88 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_UnderDriveCmd), (80 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_WakeUpPinCmd), (56 bytes).
    Removing stm32f4xx_qspi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_qspi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_qspi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockLPModeCmd), (104 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd), (104 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockLPModeCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockLPModeCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphResetCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphClockLPModeCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB2PeriphClockLPModeCmd), (104 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd), (104 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AdjustHSICalibrationValue), (68 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_BackupResetCmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearITPendingBit), (52 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClockSecuritySystemCmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetClocksFreq), (232 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetITStatus), (96 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HSICmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_I2SCLKConfig), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ITConfig), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEConfig), (96 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEModeConfig), (84 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSICmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LTDCCLKDivConfig), (80 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO1Config), (128 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO2Config), (128 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SCmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SConfig), (88 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAICmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAIConfig), (116 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKCmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKConfig), (412 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockACLKConfig), (76 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockBCLKConfig), (76 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLI2SClkDivConfig), (72 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLSAIClkDivConfig), (76 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_TIMCLKPresConfig), (56 bytes).
    Removing stm32f4xx_rcc.o(.data), (16 bytes).
    Removing stm32f4xx_rng.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rng.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rng.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rng.o(i.RNG_ClearFlag), (64 bytes).
    Removing stm32f4xx_rng.o(i.RNG_ClearITPendingBit), (64 bytes).
    Removing stm32f4xx_rng.o(i.RNG_Cmd), (80 bytes).
    Removing stm32f4xx_rng.o(i.RNG_DeInit), (20 bytes).
    Removing stm32f4xx_rng.o(i.RNG_GetFlagStatus), (76 bytes).
    Removing stm32f4xx_rng.o(i.RNG_GetITStatus), (72 bytes).
    Removing stm32f4xx_rng.o(i.RNG_GetRandomNumber), (12 bytes).
    Removing stm32f4xx_rng.o(i.RNG_ITConfig), (80 bytes).
    Removing stm32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_AlarmCmd), (180 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_AlarmStructInit), (22 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_AlarmSubSecondConfig), (232 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_Bcd2ToByte), (22 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_BypassShadowCmd), (104 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ByteToBcd2), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CalibOutputCmd), (104 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CalibOutputConfig), (96 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ClearFlag), (80 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ClearITPendingBit), (80 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CoarseCalibCmd), (124 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CoarseCalibConfig), (116 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_DateStructInit), (14 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_DayLightSavingConfig), (128 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_DeInit), (212 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_EnterInitMode), (80 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ExitInitMode), (20 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetAlarm), (184 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetAlarmSubSecond), (36 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetDate), (120 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetFlagStatus), (152 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetITStatus), (148 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetStoreOperation), (16 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetSubSecond), (20 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetTime), (124 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetTimeStamp), (192 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetTimeStampSubSecond), (12 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetWakeUpCounter), (12 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ITConfig), (172 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_Init), (184 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_OutputConfig), (136 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_OutputTypeConfig), (76 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ReadBackupRegister), (144 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_RefClockCmd), (124 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetAlarm), (684 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetDate), (420 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetTime), (452 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetWakeUpCounter), (72 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SmoothCalibConfig), (200 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_StructInit), (14 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SynchroShiftConfig), (188 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperCmd), (100 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperFilterConfig), (88 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperPinSelection), (76 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperPinsPrechargeDuration), (88 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperPullUpCmd), (80 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperSamplingFreqConfig), (112 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperTriggerConfig), (108 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStampCmd), (120 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStampOnTamperDetectionCmd), (80 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStampPinSelection), (76 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStructInit), (12 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WaitForSynchro), (96 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WakeUpClockConfig), (108 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WakeUpCmd), (164 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WriteBackupRegister), (148 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WriteProtectionCmd), (72 bytes).
    Removing stm32f4xx_sai.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sai.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sai.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_sai.o(i.SAI_ClearFlag), (108 bytes).
    Removing stm32f4xx_sai.o(i.SAI_ClearITPendingBit), (108 bytes).
    Removing stm32f4xx_sai.o(i.SAI_Cmd), (100 bytes).
    Removing stm32f4xx_sai.o(i.SAI_CompandingModeConfig), (116 bytes).
    Removing stm32f4xx_sai.o(i.SAI_DMACmd), (100 bytes).
    Removing stm32f4xx_sai.o(i.SAI_DeInit), (76 bytes).
    Removing stm32f4xx_sai.o(i.SAI_FlushFIFO), (68 bytes).
    Removing stm32f4xx_sai.o(i.SAI_FrameInit), (224 bytes).
    Removing stm32f4xx_sai.o(i.SAI_FrameStructInit), (18 bytes).
    Removing stm32f4xx_sai.o(i.SAI_GetCmdStatus), (80 bytes).
    Removing stm32f4xx_sai.o(i.SAI_GetFIFOStatus), (72 bytes).
    Removing stm32f4xx_sai.o(i.SAI_GetFlagStatus), (120 bytes).
    Removing stm32f4xx_sai.o(i.SAI_GetITStatus), (132 bytes).
    Removing stm32f4xx_sai.o(i.SAI_ITConfig), (140 bytes).
    Removing stm32f4xx_sai.o(i.SAI_Init), (432 bytes).
    Removing stm32f4xx_sai.o(i.SAI_MonoModeConfig), (80 bytes).
    Removing stm32f4xx_sai.o(i.SAI_MuteFrameCounterConfig), (96 bytes).
    Removing stm32f4xx_sai.o(i.SAI_MuteModeCmd), (100 bytes).
    Removing stm32f4xx_sai.o(i.SAI_MuteValueConfig), (96 bytes).
    Removing stm32f4xx_sai.o(i.SAI_ReceiveData), (64 bytes).
    Removing stm32f4xx_sai.o(i.SAI_SendData), (64 bytes).
    Removing stm32f4xx_sai.o(i.SAI_SlotInit), (180 bytes).
    Removing stm32f4xx_sai.o(i.SAI_SlotStructInit), (16 bytes).
    Removing stm32f4xx_sai.o(i.SAI_StructInit), (30 bytes).
    Removing stm32f4xx_sai.o(i.SAI_TRIStateConfig), (96 bytes).
    Removing stm32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_CEATAITCmd), (64 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ClearFlag), (68 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ClearITPendingBit), (68 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ClockCmd), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_CommandCompletionCmd), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_DMACmd), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_DataConfig), (268 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_DataStructInit), (20 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_DeInit), (22 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetCommandResponse), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetFlagStatus), (196 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetITStatus), (196 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetPowerState), (16 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetResponse), (80 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ITConfig), (104 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_Init), (208 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ReadData), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SendCEATACmd), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SendCommand), (180 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SendSDIOSuspendCmd), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SetPowerState), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SetSDIOOperation), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SetSDIOReadWaitMode), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_StartSDIOReadWait), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_StopSDIOReadWait), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_StructInit), (16 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_WriteData), (12 bytes).
    Removing stm32f4xx_spdifrx.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_spdifrx.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_spdifrx.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_spi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_spi.o(i.I2S_Cmd), (120 bytes).
    Removing stm32f4xx_spi.o(i.I2S_FullDuplexConfig), (276 bytes).
    Removing stm32f4xx_spi.o(i.I2S_Init), (608 bytes).
    Removing stm32f4xx_spi.o(i.I2S_StructInit), (20 bytes).
    Removing stm32f4xx_spi.o(i.SPI_BiDirectionalLineConfig), (156 bytes).
    Removing stm32f4xx_spi.o(i.SPI_CalculateCRC), (144 bytes).
    Removing stm32f4xx_spi.o(i.SPI_Cmd), (144 bytes).
    Removing stm32f4xx_spi.o(i.SPI_DataSizeConfig), (140 bytes).
    Removing stm32f4xx_spi.o(i.SPI_GetCRC), (136 bytes).
    Removing stm32f4xx_spi.o(i.SPI_GetCRCPolynomial), (104 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ClearFlag), (140 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ClearITPendingBit), (152 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_DMACmd), (180 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_DeInit), (248 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_GetFlagStatus), (188 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_GetITStatus), (220 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ITConfig), (204 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ReceiveData), (120 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_SendData), (124 bytes).
    Removing stm32f4xx_spi.o(i.SPI_Init), (416 bytes).
    Removing stm32f4xx_spi.o(i.SPI_NSSInternalSoftwareConfig), (160 bytes).
    Removing stm32f4xx_spi.o(i.SPI_SSOutputCmd), (144 bytes).
    Removing stm32f4xx_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f4xx_spi.o(i.SPI_TIModeCmd), (144 bytes).
    Removing stm32f4xx_spi.o(i.SPI_TransmitCRC), (112 bytes).
    Removing stm32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_CompensationCellCmd), (60 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_DeInit), (22 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_ETH_MediaInterfaceConfig), (60 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_EXTILineConfig), (220 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_GetCompensationCellStatus), (24 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_MemoryRemapConfig), (68 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_MemorySwappingBank), (60 bytes).
    Removing stm32f4xx_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_tim.o(i.TI1_Config), (58 bytes).
    Removing stm32f4xx_tim.o(i.TI2_Config), (80 bytes).
    Removing stm32f4xx_tim.o(i.TI3_Config), (72 bytes).
    Removing stm32f4xx_tim.o(i.TI4_Config), (80 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ARRPreloadConfig), (220 bytes).
    Removing stm32f4xx_tim.o(i.TIM_BDTRConfig), (256 bytes).
    Removing stm32f4xx_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCPreloadControl), (104 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCxCmd), (236 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCxNCmd), (136 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearFlag), (184 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC1Ref), (192 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC2Ref), (160 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC3Ref), (132 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC4Ref), (140 bytes).
    Removing stm32f4xx_tim.o(i.TIM_Cmd), (220 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CounterModeConfig), (144 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CtrlPWMOutputs), (112 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DMACmd), (180 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DMAConfig), (316 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DeInit), (516 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRClockMode1Config), (216 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRClockMode2Config), (196 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRConfig), (208 bytes).
    Removing stm32f4xx_tim.o(i.TIM_EncoderInterfaceConfig), (280 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC1Config), (196 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC2Config), (164 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC3Config), (136 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC4Config), (144 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GenerateEvent), (204 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture1), (160 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture2), (120 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture3), (100 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture4), (100 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCounter), (180 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetFlagStatus), (264 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetPrescaler), (180 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ICInit), (528 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ITConfig), (236 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ITRxExternalClockConfig), (164 bytes).
    Removing stm32f4xx_tim.o(i.TIM_InternalClockConfig), (128 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1FastConfig), (192 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1Init), (468 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1NPolarityConfig), (96 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1PolarityConfig), (192 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1PreloadConfig), (192 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2FastConfig), (160 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2Init), (468 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2NPolarityConfig), (104 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2PolarityConfig), (160 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2PreloadConfig), (160 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3FastConfig), (132 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3Init), (444 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3NPolarityConfig), (104 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3PolarityConfig), (140 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3PreloadConfig), (132 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4FastConfig), (140 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4Init), (336 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4PolarityConfig), (140 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4PreloadConfig), (140 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_PWMIConfig), (236 bytes).
    Removing stm32f4xx_tim.o(i.TIM_PrescalerConfig), (204 bytes).
    Removing stm32f4xx_tim.o(i.TIM_RemapConfig), (128 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectCCDMA), (140 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectCOM), (104 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectHallSensor), (160 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectInputTrigger), (216 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectMasterSlaveMode), (156 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOCxM), (320 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOnePulseMode), (216 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOutputTrigger), (180 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectSlaveMode), (164 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetAutoreload), (184 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetClockDivision), (204 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare1), (164 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare2), (124 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare3), (104 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare4), (104 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCounter), (184 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC1Prescaler), (204 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC2Prescaler), (172 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC3Prescaler), (144 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC4Prescaler), (152 bytes).
    Removing stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig), (252 bytes).
    Removing stm32f4xx_tim.o(i.TIM_TimeBaseInit), (356 bytes).
    Removing stm32f4xx_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_UpdateDisableConfig), (220 bytes).
    Removing stm32f4xx_tim.o(i.TIM_UpdateRequestConfig), (220 bytes).
    Removing stm32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_usart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClearFlag), (220 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClearITPendingBit), (256 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClockInit), (212 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f4xx_usart.o(i.USART_Cmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_DMACmd), (208 bytes).
    Removing stm32f4xx_usart.o(i.USART_DeInit), (344 bytes).
    Removing stm32f4xx_usart.o(i.USART_GetFlagStatus), (264 bytes).
    Removing stm32f4xx_usart.o(i.USART_GetITStatus), (372 bytes).
    Removing stm32f4xx_usart.o(i.USART_HalfDuplexCmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_ITConfig), (352 bytes).
    Removing stm32f4xx_usart.o(i.USART_Init), (556 bytes).
    Removing stm32f4xx_usart.o(i.USART_IrDACmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_IrDAConfig), (184 bytes).
    Removing stm32f4xx_usart.o(i.USART_LINBreakDetectLengthConfig), (184 bytes).
    Removing stm32f4xx_usart.o(i.USART_LINCmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_OneBitMethodCmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_OverSampling8Cmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_ReceiveData), (152 bytes).
    Removing stm32f4xx_usart.o(i.USART_ReceiverWakeUpCmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_SendBreak), (156 bytes).
    Removing stm32f4xx_usart.o(i.USART_SendData), (172 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetAddress), (180 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetGuardTime), (104 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetPrescaler), (164 bytes).
    Removing stm32f4xx_usart.o(i.USART_SmartCardCmd), (128 bytes).
    Removing stm32f4xx_usart.o(i.USART_SmartCardNACKCmd), (128 bytes).
    Removing stm32f4xx_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_WakeUpConfig), (184 bytes).
    Removing stm32f4xx_wwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_DeInit), (22 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_Enable), (64 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_EnableIT), (12 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_GetFlagStatus), (20 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_SetCounter), (64 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_SetPrescaler), (84 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_SetWindowValue), (84 bytes).
    Removing systick.o(.rev16_text), (4 bytes).
    Removing systick.o(.revsh_text), (4 bytes).
    Removing systick.o(.rrx_text), (6 bytes).
    Removing systick.o(i.Delay_s), (24 bytes).
    Removing systick.o(i.SysTick_Calibrate), (96 bytes).
    Removing systick.o(i.SysTick_GetStats), (24 bytes).
    Removing systick.o(i.SysTick_GetTimestamp_us), (56 bytes).
    Removing systick.o(i.SysTick_GetUptime_ms), (12 bytes).
    Removing systick.o(i.SysTick_NonBlocking_Init), (32 bytes).
    Removing systick.o(i.SysTick_NonBlocking_IsCompleted), (48 bytes).
    Removing systick.o(i.SysTick_SetTemperatureCompensation), (24 bytes).
    Removing ad9910_hal.o(.rev16_text), (4 bytes).
    Removing ad9910_hal.o(.revsh_text), (4 bytes).
    Removing ad9910_hal.o(.rrx_text), (6 bytes).
    Removing ad9910_waveform.o(.rev16_text), (4 bytes).
    Removing ad9910_waveform.o(.revsh_text), (4 bytes).
    Removing ad9910_waveform.o(.rrx_text), (6 bytes).
    Removing ad9910_waveform.o(i.AD9910_CalculateAmplitudeWord), (12 bytes).
    Removing ad9910_waveform.o(i.AD9910_CalculateFrequencyWord), (12 bytes).
    Removing ad9910_waveform.o(i.AD9910_Configure), (42 bytes).
    Removing ad9910_waveform.o(i.AD9910_GetConfiguration), (16 bytes).
    Removing ad9910_waveform.o(i.AD9910_ReadRegister), (2 bytes).
    Removing ad9910_waveform.o(i.AD9910_SetAmplitude), (52 bytes).
    Removing ad9910_waveform.o(i.AD9910_SetAmplitudeCalibration), (12 bytes).
    Removing ad9910_waveform.o(i.AD9910_SetFrequency), (64 bytes).
    Removing ad9910_waveform.o(i.AD9910_SetFrequencyCalibration), (12 bytes).
    Removing ad9910_waveform.o(i.AD9910_SetPhase), (64 bytes).
    Removing ad9910_waveform.o(i.AD9910_SetTemperatureCompensation), (12 bytes).
    Removing ad9910_waveform.o(i.AD9910_SetWaveType), (12 bytes).
    Removing ad9910_waveform.o(i.AD9910_SoftwareReset), (12 bytes).
    Removing ad9910_control.o(.rev16_text), (4 bytes).
    Removing ad9910_control.o(.revsh_text), (4 bytes).
    Removing ad9910_control.o(.rrx_text), (6 bytes).
    Removing ad9910_control.o(i.AD9910_Control_Execute), (228 bytes).
    Removing ad9910_control.o(i.AD9910_Control_GetParams), (28 bytes).
    Removing ad9910_control.o(i.AD9910_Control_GetRanges), (28 bytes).
    Removing ad9910_control.o(i.AD9910_Control_LoadPreset), (104 bytes).
    Removing ad9910_control.o(i.AD9910_Control_SetPreset), (64 bytes).
    Removing ad9910_control.o(i.AD9910_Control_ValidateParams), (60 bytes).
    Removing command_parser.o(.rev16_text), (4 bytes).
    Removing command_parser.o(.revsh_text), (4 bytes).
    Removing command_parser.o(.rrx_text), (6 bytes).
    Removing command_parser.o(i.CommandParser_AddUARTData), (68 bytes).
    Removing command_parser.o(i.CommandParser_ClearBuffer), (28 bytes).
    Removing command_parser.o(i.CommandParser_FormatKeypadResponse), (72 bytes).
    Removing command_parser.o(i.CommandParser_FormatUARTResponse), (72 bytes).
    Removing command_parser.o(i.CommandParser_GenerateResponse), (656 bytes).
    Removing command_parser.o(i.CommandParser_ParseKeypad), (22 bytes).
    Removing command_parser.o(i.CommandParser_ParseUART), (34 bytes).
    Removing command_parser.o(i.CommandParser_ProcessPendingUART), (112 bytes).
    Removing command_parser.o(i.ParseDoubleParameter), (32 bytes).
    Removing command_parser.o(i.ParseKeypadSequence), (232 bytes).
    Removing command_parser.o(i.ParseUARTCommand), (416 bytes).
    Removing gain_calculator.o(i.CalculateTotalGain), (192 bytes).
    Removing gain_calculator.o(i.GainCalculator_Calculate), (432 bytes).
    Removing gain_calculator.o(i.GainCalculator_CalculateDynamicRange), (116 bytes).
    Removing gain_calculator.o(i.GainCalculator_ConfigForContest), (128 bytes).
    Removing gain_calculator.o(i.GainCalculator_FrequencyCorrection), (184 bytes).
    Removing gain_calculator.o(i.GainCalculator_GetStages), (24 bytes).
    Removing gain_calculator.o(i.GainCalculator_NonlinearCorrection), (76 bytes).
    Removing gain_calculator.o(i.GainCalculator_OptimizeAccuracy), (96 bytes).
    Removing gain_calculator.o(i.GainCalculator_OptimizeStages), (272 bytes).
    Removing gain_calculator.o(i.GainCalculator_SelfTest), (44 bytes).
    Removing gain_calculator.o(i.GainCalculator_SetVariableGain), (108 bytes).
    Removing gain_calculator.o(i.GainCalculator_TemperatureCorrection), (184 bytes).
    Removing gain_calculator.o(i.GainCalculator_TransferFunctionPhase), (244 bytes).
    Removing gain_calculator.o(i.GainCalculator_ValidateGain), (144 bytes).
    Removing gain_calculator.o(i.ValidateGainConfig), (88 bytes).
    Removing gain_calculator.o(.constdata), (24 bytes).
    Removing signal_generator.o(.rev16_text), (4 bytes).
    Removing signal_generator.o(.revsh_text), (4 bytes).
    Removing signal_generator.o(.rrx_text), (6 bytes).
    Removing signal_generator.o(i.SignalGenerator_DeInit), (28 bytes).
    Removing signal_generator.o(i.SignalGenerator_GetMeasurement), (8 bytes).
    Removing signal_generator.o(i.SignalGenerator_GetParams), (8 bytes).
    Removing signal_generator.o(i.SignalGenerator_IsReady), (12 bytes).
    Removing signal_generator.o(i.SignalGenerator_StartMeasurement), (64 bytes).
    Removing matrix_keypad.o(.rev16_text), (4 bytes).
    Removing matrix_keypad.o(.revsh_text), (4 bytes).
    Removing matrix_keypad.o(.rrx_text), (6 bytes).
    Removing matrix_keypad.o(i.MatrixKeypad_DeInit), (24 bytes).
    Removing matrix_keypad.o(i.MatrixKeypad_GetKeyName), (48 bytes).
    Removing matrix_keypad.o(i.MatrixKeypad_IsReady), (12 bytes).
    Removing matrix_keypad.o(i.MatrixKeypad_Scan), (32 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(.rrx_text), (6 bytes).
    Removing key.o(i.Key_DeInit), (12 bytes).
    Removing key.o(i.Key_GPIO_Config), (44 bytes).
    Removing key.o(i.Key_GetState), (36 bytes).
    Removing key.o(i.Key_Init), (64 bytes).
    Removing key.o(i.Key_IsPressed), (20 bytes).
    Removing key.o(i.Key_IsReady), (12 bytes).
    Removing key.o(i.Key_ReadRaw), (60 bytes).
    Removing key.o(i.Key_Scan), (152 bytes).
    Removing key.o(i.Key_WaitPress), (64 bytes).
    Removing key.o(i.Key_WaitRelease), (68 bytes).
    Removing key.o(.bss), (12 bytes).
    Removing key.o(.data), (8 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.Delay_1ms), (26 bytes).
    Removing oled.o(i.Delay_50ms), (22 bytes).
    Removing oled.o(i.OLED_Diagnostic_Test), (244 bytes).
    Removing oled.o(i.OLED_Display_Off), (28 bytes).
    Removing oled.o(i.OLED_Display_On), (28 bytes).
    Removing oled.o(i.OLED_DrawBMP), (118 bytes).
    Removing oled.o(i.OLED_DrawPoint), (104 bytes).
    Removing oled.o(i.OLED_Fill), (58 bytes).
    Removing oled.o(i.OLED_Force_Clear), (104 bytes).
    Removing oled.o(i.OLED_ShowCHinese), (82 bytes).
    Removing oled.o(i.OLED_ShowNum), (132 bytes).
    Removing oled.o(i.OLED_Simple_Test), (228 bytes).
    Removing oled.o(i.OLED_Test_Display), (144 bytes).
    Removing oled.o(i.fill_picture), (66 bytes).
    Removing oled.o(i.mypow), (22 bytes).
    Removing oled_font.o(.rev16_text), (4 bytes).
    Removing oled_font.o(.revsh_text), (4 bytes).
    Removing oled_font.o(.rrx_text), (6 bytes).

944 unused section(s) (total 103366 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/dczerorl2.s                0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/ctype.c                          0x00000000   Number         0  isspace.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memmove_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memmove_v6.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2snprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2snprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtod.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _strtoul.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_infnan.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_fp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtoul.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  atol.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  atoi.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_hexfp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtol.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strchr.o ABSOLUTE
    ../fplib/basic.s                         0x00000000   Number         0  basic.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/deqf.s                          0x00000000   Number         0  deqf.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/dsqrt.s                         0x00000000   Number         0  dsqrt_umaal.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpconst.s                       0x00000000   Number         0  fpconst.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/scanf1.s                        0x00000000   Number         0  scanf1.o ABSOLUTE
    ../fplib/scanf2.s                        0x00000000   Number         0  scanf2.o ABSOLUTE
    ../fplib/scanf2a.s                       0x00000000   Number         0  scanf2a.o ABSOLUTE
    ../fplib/scanf2b.s                       0x00000000   Number         0  scanf2b.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/frexp.c                       0x00000000   Number         0  frexp.o ABSOLUTE
    ../mathlib/ldexp.c                       0x00000000   Number         0  ldexp_x.o ABSOLUTE
    ../mathlib/ldexp.c                       0x00000000   Number         0  ldexp.o ABSOLUTE
    ../mathlib/log.c                         0x00000000   Number         0  log_x.o ABSOLUTE
    ../mathlib/log.c                         0x00000000   Number         0  log.o ABSOLUTE
    ../mathlib/log10.c                       0x00000000   Number         0  log10.o ABSOLUTE
    ../mathlib/log10.c                       0x00000000   Number         0  log10_x.o ABSOLUTE
    ../mathlib/narrow.c                      0x00000000   Number         0  narrow.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ../mathlib/strtod.c                      0x00000000   Number         0  strtod.o ABSOLUTE
    Library\\misc.c                          0x00000000   Number         0  misc.o ABSOLUTE
    Library\\stm32f4xx_adc.c                 0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    Library\\stm32f4xx_can.c                 0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    Library\\stm32f4xx_cec.c                 0x00000000   Number         0  stm32f4xx_cec.o ABSOLUTE
    Library\\stm32f4xx_crc.c                 0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    Library\\stm32f4xx_cryp.c                0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    Library\\stm32f4xx_cryp_aes.c            0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    Library\\stm32f4xx_cryp_des.c            0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    Library\\stm32f4xx_cryp_tdes.c           0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    Library\\stm32f4xx_dac.c                 0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    Library\\stm32f4xx_dbgmcu.c              0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    Library\\stm32f4xx_dcmi.c                0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    Library\\stm32f4xx_dfsdm.c               0x00000000   Number         0  stm32f4xx_dfsdm.o ABSOLUTE
    Library\\stm32f4xx_dma.c                 0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    Library\\stm32f4xx_dma2d.c               0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    Library\\stm32f4xx_dsi.c                 0x00000000   Number         0  stm32f4xx_dsi.o ABSOLUTE
    Library\\stm32f4xx_exti.c                0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    Library\\stm32f4xx_flash.c               0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    Library\\stm32f4xx_flash_ramfunc.c       0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    Library\\stm32f4xx_fmpi2c.c              0x00000000   Number         0  stm32f4xx_fmpi2c.o ABSOLUTE
    Library\\stm32f4xx_fsmc.c                0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    Library\\stm32f4xx_gpio.c                0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    Library\\stm32f4xx_hash.c                0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    Library\\stm32f4xx_hash_md5.c            0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    Library\\stm32f4xx_hash_sha1.c           0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    Library\\stm32f4xx_i2c.c                 0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    Library\\stm32f4xx_iwdg.c                0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    Library\\stm32f4xx_lptim.c               0x00000000   Number         0  stm32f4xx_lptim.o ABSOLUTE
    Library\\stm32f4xx_ltdc.c                0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    Library\\stm32f4xx_pwr.c                 0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    Library\\stm32f4xx_qspi.c                0x00000000   Number         0  stm32f4xx_qspi.o ABSOLUTE
    Library\\stm32f4xx_rcc.c                 0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    Library\\stm32f4xx_rng.c                 0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    Library\\stm32f4xx_rtc.c                 0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    Library\\stm32f4xx_sai.c                 0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    Library\\stm32f4xx_sdio.c                0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    Library\\stm32f4xx_spdifrx.c             0x00000000   Number         0  stm32f4xx_spdifrx.o ABSOLUTE
    Library\\stm32f4xx_spi.c                 0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    Library\\stm32f4xx_syscfg.c              0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    Library\\stm32f4xx_tim.c                 0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    Library\\stm32f4xx_usart.c               0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    Library\\stm32f4xx_wwdg.c                0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    Library\misc.c                           0x00000000   Number         0  misc.o ABSOLUTE
    Library\stm32f4xx_adc.c                  0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    Library\stm32f4xx_can.c                  0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    Library\stm32f4xx_cec.c                  0x00000000   Number         0  stm32f4xx_cec.o ABSOLUTE
    Library\stm32f4xx_crc.c                  0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    Library\stm32f4xx_cryp.c                 0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    Library\stm32f4xx_cryp_aes.c             0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    Library\stm32f4xx_cryp_des.c             0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    Library\stm32f4xx_cryp_tdes.c            0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    Library\stm32f4xx_dac.c                  0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    Library\stm32f4xx_dbgmcu.c               0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    Library\stm32f4xx_dcmi.c                 0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    Library\stm32f4xx_dfsdm.c                0x00000000   Number         0  stm32f4xx_dfsdm.o ABSOLUTE
    Library\stm32f4xx_dma.c                  0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    Library\stm32f4xx_dma2d.c                0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    Library\stm32f4xx_dsi.c                  0x00000000   Number         0  stm32f4xx_dsi.o ABSOLUTE
    Library\stm32f4xx_exti.c                 0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    Library\stm32f4xx_flash.c                0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    Library\stm32f4xx_flash_ramfunc.c        0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    Library\stm32f4xx_fmpi2c.c               0x00000000   Number         0  stm32f4xx_fmpi2c.o ABSOLUTE
    Library\stm32f4xx_fsmc.c                 0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    Library\stm32f4xx_gpio.c                 0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    Library\stm32f4xx_hash.c                 0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    Library\stm32f4xx_hash_md5.c             0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    Library\stm32f4xx_hash_sha1.c            0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    Library\stm32f4xx_i2c.c                  0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    Library\stm32f4xx_iwdg.c                 0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    Library\stm32f4xx_lptim.c                0x00000000   Number         0  stm32f4xx_lptim.o ABSOLUTE
    Library\stm32f4xx_ltdc.c                 0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    Library\stm32f4xx_pwr.c                  0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    Library\stm32f4xx_qspi.c                 0x00000000   Number         0  stm32f4xx_qspi.o ABSOLUTE
    Library\stm32f4xx_rcc.c                  0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    Library\stm32f4xx_rng.c                  0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    Library\stm32f4xx_rtc.c                  0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    Library\stm32f4xx_sai.c                  0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    Library\stm32f4xx_sdio.c                 0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    Library\stm32f4xx_spdifrx.c              0x00000000   Number         0  stm32f4xx_spdifrx.o ABSOLUTE
    Library\stm32f4xx_spi.c                  0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    Library\stm32f4xx_syscfg.c               0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    Library\stm32f4xx_tim.c                  0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    Library\stm32f4xx_usart.c                0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    Library\stm32f4xx_wwdg.c                 0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    Modules\Control\ad9910_control.c         0x00000000   Number         0  ad9910_control.o ABSOLUTE
    Modules\Control\command_parser.c         0x00000000   Number         0  command_parser.o ABSOLUTE
    Modules\Control\gain_calculator.c        0x00000000   Number         0  gain_calculator.o ABSOLUTE
    Modules\Control\signal_generator.c       0x00000000   Number         0  signal_generator.o ABSOLUTE
    Modules\Core\systick.c                   0x00000000   Number         0  systick.o ABSOLUTE
    Modules\Generation\ad9910_hal.c          0x00000000   Number         0  ad9910_hal.o ABSOLUTE
    Modules\Generation\ad9910_waveform.c     0x00000000   Number         0  ad9910_waveform.o ABSOLUTE
    Modules\Interface\key.c                  0x00000000   Number         0  key.o ABSOLUTE
    Modules\Interface\matrix_keypad.c        0x00000000   Number         0  matrix_keypad.o ABSOLUTE
    Modules\Interface\oled.c                 0x00000000   Number         0  oled.o ABSOLUTE
    Modules\Interface\oled_font.c            0x00000000   Number         0  oled_font.o ABSOLUTE
    Modules\\Control\\ad9910_control.c       0x00000000   Number         0  ad9910_control.o ABSOLUTE
    Modules\\Control\\command_parser.c       0x00000000   Number         0  command_parser.o ABSOLUTE
    Modules\\Control\\signal_generator.c     0x00000000   Number         0  signal_generator.o ABSOLUTE
    Modules\\Core\\systick.c                 0x00000000   Number         0  systick.o ABSOLUTE
    Modules\\Generation\\ad9910_hal.c        0x00000000   Number         0  ad9910_hal.o ABSOLUTE
    Modules\\Generation\\ad9910_waveform.c   0x00000000   Number         0  ad9910_waveform.o ABSOLUTE
    Modules\\Interface\\key.c                0x00000000   Number         0  key.o ABSOLUTE
    Modules\\Interface\\matrix_keypad.c      0x00000000   Number         0  matrix_keypad.o ABSOLUTE
    Modules\\Interface\\oled.c               0x00000000   Number         0  oled.o ABSOLUTE
    Modules\\Interface\\oled_font.c          0x00000000   Number         0  oled_font.o ABSOLUTE
    Start\startup_stm32f40_41xxx.s           0x00000000   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    User\\bsp.c                              0x00000000   Number         0  bsp.o ABSOLUTE
    User\\main.c                             0x00000000   Number         0  main.o ABSOLUTE
    User\\stm32f4xx_it.c                     0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    User\\system_stm32f4xx.c                 0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    User\bsp.c                               0x00000000   Number         0  bsp.o ABSOLUTE
    User\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    User\stm32f4xx_it.c                      0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    User\system_stm32f4xx.c                  0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f40_41xxx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!dczerorl2                              0x080001c4   Section       90  __dczerorl2.o(!!dczerorl2)
    !!handler_zi                             0x08000220   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x0800023c   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000003  0x0800023c   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000009  0x08000242   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x08000248   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$00000014  0x0800024e   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000017  0x08000254   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x08000258   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x0800025a   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x0800025e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x0800025e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x0800025e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x0800025e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x0800025e   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x08000264   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x08000264   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x08000270   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000270   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x08000270   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x0800027a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x0800027a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0800027a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0800027a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0800027a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0800027a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0800027a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x0800027a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x0800027a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x0800027a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0800027a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0800027a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x0800027a   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x0800027c   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x0800027e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x0800027e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x0800027e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x0800027e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x0800027e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x0800027e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x0800027e   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000280   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000280   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000280   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000286   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000286   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800028a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800028a   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000292   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000294   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000294   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000298   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080002a0   Section       64  startup_stm32f40_41xxx.o(.text)
    $v0                                      0x080002a0   Number         0  startup_stm32f40_41xxx.o(.text)
    .text                                    0x080002e0   Section        0  __2sprintf.o(.text)
    .text                                    0x0800030c   Section        0  _printf_str.o(.text)
    .text                                    0x08000360   Section        0  _printf_dec.o(.text)
    .text                                    0x080003d8   Section        0  __printf_ss_wp.o(.text)
    .text                                    0x08000538   Section        0  atoi.o(.text)
    .text                                    0x08000552   Section        0  atol.o(.text)
    .text                                    0x0800056c   Section        0  strcpy.o(.text)
    .text                                    0x080005b4   Section        0  strlen.o(.text)
    .text                                    0x080005f2   Section       68  rt_memclr.o(.text)
    .text                                    0x08000636   Section       78  rt_memclr_w.o(.text)
    .text                                    0x08000684   Section        0  heapauxi.o(.text)
    .text                                    0x0800068c   Section       16  rt_ctype_table.o(.text)
    .text                                    0x0800069c   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x080006a4   Section        0  _rserrno.o(.text)
    .text                                    0x080006ba   Section        0  _printf_intcommon.o(.text)
    .text                                    0x0800076c   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x0800076f   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08000b8c   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000b8d   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000bbc   Section        0  _sputc.o(.text)
    .text                                    0x08000bc6   Section        0  _printf_char.o(.text)
    .text                                    0x08000bf2   Section        0  _strtoul.o(.text)
    .text                                    0x08000c90   Section        0  strtol.o(.text)
    .text                                    0x08000d00   Section        8  libspace.o(.text)
    .text                                    0x08000d08   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08000d10   Section      138  lludiv10.o(.text)
    .text                                    0x08000d9c   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08000e1c   Section        0  _chval.o(.text)
    .text                                    0x08000e38   Section        0  bigflt0.o(.text)
    .text                                    0x08000f1c   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000f66   Section        0  exit.o(.text)
    .text                                    0x08000f78   Section      128  strcmpv7m.o(.text)
    .text                                    0x08000ff8   Section        0  sys_exit.o(.text)
    .text                                    0x08001004   Section        2  use_no_semi.o(.text)
    .text                                    0x08001006   Section        0  indicate_semi.o(.text)
    CL$$btod_d2e                             0x08001006   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08001044   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x0800108a   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x080010ea   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x08001422   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x080014fe   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08001528   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x08001552   Section      580  btod.o(CL$$btod_mult_common)
    i.AD9910_CalculateAmplitudeWord_Precision 0x08001798   Section        0  ad9910_waveform.o(i.AD9910_CalculateAmplitudeWord_Precision)
    AD9910_CalculateAmplitudeWord_Precision  0x08001799   Thumb Code   274  ad9910_waveform.o(i.AD9910_CalculateAmplitudeWord_Precision)
    i.AD9910_CalculateFrequencyWord_Precision 0x08001900   Section        0  ad9910_waveform.o(i.AD9910_CalculateFrequencyWord_Precision)
    AD9910_CalculateFrequencyWord_Precision  0x08001901   Thumb Code   172  ad9910_waveform.o(i.AD9910_CalculateFrequencyWord_Precision)
    i.AD9910_Control_CalculateActualOutput   0x080019dc   Section        0  ad9910_control.o(i.AD9910_Control_CalculateActualOutput)
    i.AD9910_Control_CalculateTargetOutput   0x08001a78   Section        0  ad9910_control.o(i.AD9910_Control_CalculateTargetOutput)
    i.AD9910_Control_EnableOutput            0x08001ac8   Section        0  ad9910_control.o(i.AD9910_Control_EnableOutput)
    i.AD9910_Control_Init                    0x08001ae8   Section        0  ad9910_control.o(i.AD9910_Control_Init)
    i.AD9910_Control_RegisterCallback        0x08001bc4   Section        0  ad9910_control.o(i.AD9910_Control_RegisterCallback)
    i.AD9910_Control_SetFrequency            0x08001bd0   Section        0  ad9910_control.o(i.AD9910_Control_SetFrequency)
    i.AD9910_Control_SetGainFactor           0x08001bf8   Section        0  ad9910_control.o(i.AD9910_Control_SetGainFactor)
    i.AD9910_Control_SetTargetAmplitude      0x08001c74   Section        0  ad9910_control.o(i.AD9910_Control_SetTargetAmplitude)
    i.AD9910_Control_Task                    0x08001cc0   Section        0  ad9910_control.o(i.AD9910_Control_Task)
    i.AD9910_DisableOutput                   0x08001cc4   Section        0  ad9910_waveform.o(i.AD9910_DisableOutput)
    i.AD9910_EnableInverseSincFilter         0x08001cd8   Section        0  ad9910_waveform.o(i.AD9910_EnableInverseSincFilter)
    i.AD9910_EnableOutput                    0x08001d20   Section        0  ad9910_waveform.o(i.AD9910_EnableOutput)
    i.AD9910_EnsurePhaseContinuity           0x08001d34   Section        0  ad9910_waveform.o(i.AD9910_EnsurePhaseContinuity)
    i.AD9910_HAL_DelayMs                     0x08001d6c   Section        0  ad9910_hal.o(i.AD9910_HAL_DelayMs)
    i.AD9910_HAL_DelayUs                     0x08001d78   Section        0  ad9910_hal.o(i.AD9910_HAL_DelayUs)
    i.AD9910_HAL_IOUpdate                    0x08001d90   Section        0  ad9910_hal.o(i.AD9910_HAL_IOUpdate)
    i.AD9910_HAL_Init                        0x08001db0   Section        0  ad9910_hal.o(i.AD9910_HAL_Init)
    i.AD9910_HAL_PowerControl                0x08001e88   Section        0  ad9910_hal.o(i.AD9910_HAL_PowerControl)
    i.AD9910_HAL_Reset                       0x08001eac   Section        0  ad9910_hal.o(i.AD9910_HAL_Reset)
    i.AD9910_HAL_SelectProfile               0x08001ed0   Section        0  ad9910_hal.o(i.AD9910_HAL_SelectProfile)
    i.AD9910_HAL_Send8Bits                   0x08001f28   Section        0  ad9910_hal.o(i.AD9910_HAL_Send8Bits)
    i.AD9910_Init                            0x08001f74   Section        0  ad9910_waveform.o(i.AD9910_Init)
    i.AD9910_OptimizeOutputFilter            0x08002060   Section        0  ad9910_waveform.o(i.AD9910_OptimizeOutputFilter)
    i.AD9910_OptimizeSmoothness              0x08002068   Section        0  ad9910_waveform.o(i.AD9910_OptimizeSmoothness)
    i.AD9910_OptimizeWaveform                0x08002084   Section        0  ad9910_waveform.o(i.AD9910_OptimizeWaveform)
    i.AD9910_Set5MHz_HighQuality             0x080020d0   Section        0  ad9910_waveform.o(i.AD9910_Set5MHz_HighQuality)
    i.AD9910_SetAmplitude_Precision          0x080020f8   Section        0  ad9910_waveform.o(i.AD9910_SetAmplitude_Precision)
    i.AD9910_SetFrequency_Precision          0x0800212c   Section        0  ad9910_waveform.o(i.AD9910_SetFrequency_Precision)
    i.AD9910_WriteProfile                    0x0800216c   Section        0  ad9910_waveform.o(i.AD9910_WriteProfile)
    AD9910_WriteProfile                      0x0800216d   Thumb Code    54  ad9910_waveform.o(i.AD9910_WriteProfile)
    i.AD9910_WriteRegister                   0x080021ac   Section        0  ad9910_waveform.o(i.AD9910_WriteRegister)
    i.BSP_Init                               0x080021f8   Section        0  bsp.o(i.BSP_Init)
    i.BusFault_Handler                       0x08002224   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.CommandParser_Init                     0x08002228   Section        0  command_parser.o(i.CommandParser_Init)
    i.ControlCallback                        0x08002244   Section        0  main.o(i.ControlCallback)
    i.DWT_Init                               0x08002248   Section        0  systick.o(i.DWT_Init)
    i.DebugMon_Handler                       0x080022a4   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Delay_ms                               0x080022a8   Section        0  systick.o(i.Delay_ms)
    i.Delay_us                               0x080022d4   Section        0  systick.o(i.Delay_us)
    i.EXTI0_IRQHandler                       0x08002324   Section        0  stm32f4xx_it.o(i.EXTI0_IRQHandler)
    i.EXTI0_IRQHandler_Internal              0x0800232c   Section        0  main.o(i.EXTI0_IRQHandler_Internal)
    i.FLASH_SetLatency                       0x08002330   Section        0  stm32f4xx_flash.o(i.FLASH_SetLatency)
    i.GPIO_Init                              0x080023a4   Section        0  stm32f4xx_gpio.o(i.GPIO_Init)
    i.GPIO_ReadInputDataBit                  0x0800254c   Section        0  stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit)
    i.GPIO_ResetBits                         0x08002658   Section        0  stm32f4xx_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x08002708   Section        0  stm32f4xx_gpio.o(i.GPIO_SetBits)
    i.GainCalculator_GetAD9910Output         0x080027b8   Section        0  gain_calculator.o(i.GainCalculator_GetAD9910Output)
    i.GainCalculator_GetFinalOutput          0x08002870   Section        0  gain_calculator.o(i.GainCalculator_GetFinalOutput)
    i.GainCalculator_Init                    0x080028c0   Section        0  gain_calculator.o(i.GainCalculator_Init)
    i.GainCalculator_TransferFunctionGain    0x08002924   Section        0  gain_calculator.o(i.GainCalculator_TransferFunctionGain)
    i.HardFault_Handler                      0x08002a18   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.IIC_Send_Byte                          0x08002a1c   Section        0  oled.o(i.IIC_Send_Byte)
    i.IIC_Start                              0x08002a78   Section        0  oled.o(i.IIC_Start)
    i.IIC_Stop                               0x08002aac   Section        0  oled.o(i.IIC_Stop)
    i.IIC_Wait_Ack                           0x08002ae8   Section        0  oled.o(i.IIC_Wait_Ack)
    i.MatrixKeypad_GPIO_Init                 0x08002b38   Section        0  matrix_keypad.o(i.MatrixKeypad_GPIO_Init)
    MatrixKeypad_GPIO_Init                   0x08002b39   Thumb Code   154  matrix_keypad.o(i.MatrixKeypad_GPIO_Init)
    i.MatrixKeypad_Init                      0x08002bd8   Section        0  matrix_keypad.o(i.MatrixKeypad_Init)
    i.MatrixKeypad_ReadColumn                0x08002bf8   Section        0  matrix_keypad.o(i.MatrixKeypad_ReadColumn)
    MatrixKeypad_ReadColumn                  0x08002bf9   Thumb Code    54  matrix_keypad.o(i.MatrixKeypad_ReadColumn)
    i.MatrixKeypad_ScanAll                   0x08002c34   Section        0  matrix_keypad.o(i.MatrixKeypad_ScanAll)
    i.MatrixKeypad_ScanSingle                0x08002c88   Section        0  matrix_keypad.o(i.MatrixKeypad_ScanSingle)
    MatrixKeypad_ScanSingle                  0x08002c89   Thumb Code   206  matrix_keypad.o(i.MatrixKeypad_ScanSingle)
    i.MatrixKeypad_SetRowOutput              0x08002d58   Section        0  matrix_keypad.o(i.MatrixKeypad_SetRowOutput)
    MatrixKeypad_SetRowOutput                0x08002d59   Thumb Code    60  matrix_keypad.o(i.MatrixKeypad_SetRowOutput)
    i.MemManage_Handler                      0x08002d98   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08002d9c   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.NVIC_SetPriority                       0x08002da0   Section        0  systick.o(i.NVIC_SetPriority)
    NVIC_SetPriority                         0x08002da1   Thumb Code    32  systick.o(i.NVIC_SetPriority)
    i.OLED_Clear                             0x08002dc8   Section        0  oled.o(i.OLED_Clear)
    i.OLED_GetCharFont                       0x08002df8   Section        0  oled_font.o(i.OLED_GetCharFont)
    i.OLED_Init                              0x08002e18   Section        0  oled.o(i.OLED_Init)
    i.OLED_Refresh_Gram                      0x08002f58   Section        0  oled.o(i.OLED_Refresh_Gram)
    i.OLED_Set_Pos                           0x08002fa4   Section        0  oled.o(i.OLED_Set_Pos)
    i.OLED_ShowChar                          0x08002fd0   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowString                        0x0800306c   Section        0  oled.o(i.OLED_ShowString)
    i.OLED_WR_Byte                           0x080030a2   Section        0  oled.o(i.OLED_WR_Byte)
    i.PendSV_Handler                         0x080030da   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.PrintGainCalculationDetails            0x080030dc   Section        0  main.o(i.PrintGainCalculationDetails)
    i.RCC_AHB1PeriphClockCmd                 0x08003204   Section        0  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x0800326c   Section        0  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_DeInit                             0x080032d4   Section        0  stm32f4xx_rcc.o(i.RCC_DeInit)
    i.RCC_GetFlagStatus                      0x08003338   Section        0  stm32f4xx_rcc.o(i.RCC_GetFlagStatus)
    i.RCC_GetSYSCLKSource                    0x080033dc   Section        0  stm32f4xx_rcc.o(i.RCC_GetSYSCLKSource)
    i.RCC_HCLKConfig                         0x080033ec   Section        0  stm32f4xx_rcc.o(i.RCC_HCLKConfig)
    i.RCC_HSEConfig                          0x0800344c   Section        0  stm32f4xx_rcc.o(i.RCC_HSEConfig)
    i.RCC_PCLK1Config                        0x08003490   Section        0  stm32f4xx_rcc.o(i.RCC_PCLK1Config)
    i.RCC_PCLK2Config                        0x080034e8   Section        0  stm32f4xx_rcc.o(i.RCC_PCLK2Config)
    i.RCC_PLLCmd                             0x08003540   Section        0  stm32f4xx_rcc.o(i.RCC_PLLCmd)
    i.RCC_PLLConfig                          0x08003578   Section        0  stm32f4xx_rcc.o(i.RCC_PLLConfig)
    i.RCC_SYSCLKConfig                       0x08003630   Section        0  stm32f4xx_rcc.o(i.RCC_SYSCLKConfig)
    i.RCC_WaitForHSEStartUp                  0x08003678   Section        0  stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp)
    i.SVC_Handler                            0x080036b0   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SetSysClock                            0x080036b4   Section        0  system_stm32f4xx.o(i.SetSysClock)
    SetSysClock                              0x080036b5   Thumb Code   220  system_stm32f4xx.o(i.SetSysClock)
    i.SignalGenerator_ClearInputBuffer       0x080037a0   Section        0  signal_generator.o(i.SignalGenerator_ClearInputBuffer)
    SignalGenerator_ClearInputBuffer         0x080037a1   Thumb Code    38  signal_generator.o(i.SignalGenerator_ClearInputBuffer)
    i.SignalGenerator_DisplayAmpSet          0x080037cc   Section        0  signal_generator.o(i.SignalGenerator_DisplayAmpSet)
    SignalGenerator_DisplayAmpSet            0x080037cd   Thumb Code   116  signal_generator.o(i.SignalGenerator_DisplayAmpSet)
    i.SignalGenerator_DisplayFreqSet         0x08003878   Section        0  signal_generator.o(i.SignalGenerator_DisplayFreqSet)
    SignalGenerator_DisplayFreqSet           0x08003879   Thumb Code    82  signal_generator.o(i.SignalGenerator_DisplayFreqSet)
    i.SignalGenerator_DisplayMain            0x08003904   Section        0  signal_generator.o(i.SignalGenerator_DisplayMain)
    SignalGenerator_DisplayMain              0x08003905   Thumb Code   188  signal_generator.o(i.SignalGenerator_DisplayMain)
    i.SignalGenerator_DisplayMeasurement     0x08003a0c   Section        0  signal_generator.o(i.SignalGenerator_DisplayMeasurement)
    SignalGenerator_DisplayMeasurement       0x08003a0d   Thumb Code   162  signal_generator.o(i.SignalGenerator_DisplayMeasurement)
    i.SignalGenerator_DisplayWaveSet         0x08003af8   Section        0  signal_generator.o(i.SignalGenerator_DisplayWaveSet)
    SignalGenerator_DisplayWaveSet           0x08003af9   Thumb Code    66  signal_generator.o(i.SignalGenerator_DisplayWaveSet)
    i.SignalGenerator_Init                   0x08003b70   Section        0  signal_generator.o(i.SignalGenerator_Init)
    i.SignalGenerator_ParseAmplitude         0x08003ba8   Section        0  signal_generator.o(i.SignalGenerator_ParseAmplitude)
    SignalGenerator_ParseAmplitude           0x08003ba9   Thumb Code    32  signal_generator.o(i.SignalGenerator_ParseAmplitude)
    i.SignalGenerator_ParseFrequency         0x08003bcc   Section        0  signal_generator.o(i.SignalGenerator_ParseFrequency)
    SignalGenerator_ParseFrequency           0x08003bcd   Thumb Code    32  signal_generator.o(i.SignalGenerator_ParseFrequency)
    i.SignalGenerator_Process                0x08003bf0   Section        0  signal_generator.o(i.SignalGenerator_Process)
    i.SignalGenerator_ProcessDigitInput      0x08003d38   Section        0  signal_generator.o(i.SignalGenerator_ProcessDigitInput)
    SignalGenerator_ProcessDigitInput        0x08003d39   Thumb Code   146  signal_generator.o(i.SignalGenerator_ProcessDigitInput)
    i.SignalGenerator_ProcessKeypad1         0x08003dd0   Section        0  signal_generator.o(i.SignalGenerator_ProcessKeypad1)
    SignalGenerator_ProcessKeypad1           0x08003dd1   Thumb Code   290  signal_generator.o(i.SignalGenerator_ProcessKeypad1)
    i.SignalGenerator_ProcessKeypad2         0x08003efc   Section        0  signal_generator.o(i.SignalGenerator_ProcessKeypad2)
    SignalGenerator_ProcessKeypad2           0x08003efd   Thumb Code   142  signal_generator.o(i.SignalGenerator_ProcessKeypad2)
    i.SignalGenerator_SetAmplitude           0x08003f90   Section        0  signal_generator.o(i.SignalGenerator_SetAmplitude)
    i.SignalGenerator_SetFrequency           0x08003fc4   Section        0  signal_generator.o(i.SignalGenerator_SetFrequency)
    i.SignalGenerator_SetWaveType            0x08003ff8   Section        0  signal_generator.o(i.SignalGenerator_SetWaveType)
    i.SignalGenerator_UpdateDisplay          0x08004024   Section        0  signal_generator.o(i.SignalGenerator_UpdateDisplay)
    i.SignalGenerator_UpdateParameters       0x0800407c   Section        0  signal_generator.o(i.SignalGenerator_UpdateParameters)
    SignalGenerator_UpdateParameters         0x0800407d   Thumb Code    68  signal_generator.o(i.SignalGenerator_UpdateParameters)
    i.SysTick_GetCalibratedDelay             0x080040c4   Section        0  systick.o(i.SysTick_GetCalibratedDelay)
    SysTick_GetCalibratedDelay               0x080040c5   Thumb Code    62  systick.o(i.SysTick_GetCalibratedDelay)
    i.SysTick_GetTick                        0x0800410c   Section        0  systick.o(i.SysTick_GetTick)
    i.SysTick_Handler                        0x08004118   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SysTick_Handler_Internal               0x08004134   Section        0  systick.o(i.SysTick_Handler_Internal)
    i.SysTick_Init                           0x0800416c   Section        0  systick.o(i.SysTick_Init)
    i.SysTick_ResetStats                     0x080041e8   Section        0  systick.o(i.SysTick_ResetStats)
    i.SysTick_UpdateStats                    0x08004208   Section        0  systick.o(i.SysTick_UpdateStats)
    SysTick_UpdateStats                      0x08004209   Thumb Code    80  systick.o(i.SysTick_UpdateStats)
    i.SystemClock_Config                     0x08004268   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x080042d4   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TIM2_IRQHandler                        0x0800433c   Section        0  stm32f4xx_it.o(i.TIM2_IRQHandler)
    i.TIM6_DAC_IRQHandler                    0x08004352   Section        0  stm32f4xx_it.o(i.TIM6_DAC_IRQHandler)
    i.TIM_ClearITPendingBit                  0x08004354   Section        0  stm32f4xx_tim.o(i.TIM_ClearITPendingBit)
    i.TIM_GetITStatus                        0x0800440c   Section        0  stm32f4xx_tim.o(i.TIM_GetITStatus)
    i.TimingDelay_Decrement                  0x08004514   Section        0  main.o(i.TimingDelay_Decrement)
    i.USART2_IRQHandler                      0x08004516   Section        0  stm32f4xx_it.o(i.USART2_IRQHandler)
    i.UpdateSystemTime                       0x08004518   Section        0  ad9910_control.o(i.UpdateSystemTime)
    UpdateSystemTime                         0x08004519   Thumb Code    12  ad9910_control.o(i.UpdateSystemTime)
    i.UsageFault_Handler                     0x08004528   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.ValidateAmplitude                      0x0800452c   Section        0  ad9910_control.o(i.ValidateAmplitude)
    ValidateAmplitude                        0x0800452d   Thumb Code    26  ad9910_control.o(i.ValidateAmplitude)
    i.ValidateFrequency                      0x0800454c   Section        0  ad9910_control.o(i.ValidateFrequency)
    ValidateFrequency                        0x0800454d   Thumb Code    26  ad9910_control.o(i.ValidateFrequency)
    i.ValidateGainFactor                     0x0800456c   Section        0  ad9910_control.o(i.ValidateGainFactor)
    ValidateGainFactor                       0x0800456d   Thumb Code    82  ad9910_control.o(i.ValidateGainFactor)
    i.__ARM_fpclassify                       0x080045c4   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__hardfp_sqrt                          0x080045f4   Section        0  sqrt.o(i.__hardfp_sqrt)
    i._is_digit                              0x0800466e   Section        0  __printf_wp.o(i._is_digit)
    i.assert_failed                          0x0800467c   Section        0  main.o(i.assert_failed)
    i.main                                   0x08004680   Section        0  main.o(i.main)
    locale$$code                             0x0800477c   Section       44  lc_ctype_c.o(locale$$code)
    locale$$code                             0x080047a8   Section       44  lc_numeric_c.o(locale$$code)
    x$fpl$dadd                               0x080047d4   Section      336  daddsub_clz.o(x$fpl$dadd)
    $v0                                      0x080047d4   Number         0  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x080047e5   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcmpinf                            0x08004924   Section       24  dcmpi.o(x$fpl$dcmpinf)
    $v0                                      0x08004924   Number         0  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$ddiv                               0x0800493c   Section      688  ddiv.o(x$fpl$ddiv)
    $v0                                      0x0800493c   Number         0  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x08004943   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dfixu                              0x08004bec   Section       90  dfixu.o(x$fpl$dfixu)
    $v0                                      0x08004bec   Number         0  dfixu.o(x$fpl$dfixu)
    x$fpl$dflt                               0x08004c46   Section       46  dflt_clz.o(x$fpl$dflt)
    $v0                                      0x08004c46   Number         0  dflt_clz.o(x$fpl$dflt)
    x$fpl$dfltu                              0x08004c74   Section       38  dflt_clz.o(x$fpl$dfltu)
    $v0                                      0x08004c74   Number         0  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dleqf                              0x08004c9c   Section      120  dleqf.o(x$fpl$dleqf)
    $v0                                      0x08004c9c   Number         0  dleqf.o(x$fpl$dleqf)
    x$fpl$dmul                               0x08004d14   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x08004d14   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x08004e68   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x08004e68   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x08004f04   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x08004f04   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$drleqf                             0x08004f10   Section      108  drleqf.o(x$fpl$drleqf)
    $v0                                      0x08004f10   Number         0  drleqf.o(x$fpl$drleqf)
    x$fpl$drsb                               0x08004f7c   Section       22  daddsub_clz.o(x$fpl$drsb)
    $v0                                      0x08004f7c   Number         0  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsqrt                              0x08004f94   Section      408  dsqrt_umaal.o(x$fpl$dsqrt)
    $v0                                      0x08004f94   Number         0  dsqrt_umaal.o(x$fpl$dsqrt)
    x$fpl$dsub                               0x0800512c   Section      468  daddsub_clz.o(x$fpl$dsub)
    $v0                                      0x0800512c   Number         0  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x0800513d   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$f2d                                0x08005300   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x08005300   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x08005356   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x08005356   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x080053e2   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x080053e2   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$printf1                            0x080053ec   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x080053ec   Number         0  printf1.o(x$fpl$printf1)
    .constdata                               0x080053f0   Section       12  ad9910_waveform.o(.constdata)
    x$fpl$usenofp                            0x080053f0   Section        0  usenofp.o(x$fpl$usenofp)
    cfr1_data                                0x080053f0   Data           4  ad9910_waveform.o(.constdata)
    cfr2_data                                0x080053f4   Data           4  ad9910_waveform.o(.constdata)
    cfr3_data                                0x080053f8   Data           4  ad9910_waveform.o(.constdata)
    .constdata                               0x080053fc   Section       64  matrix_keypad.o(.constdata)
    s_keypad_configs                         0x080053fc   Data          64  matrix_keypad.o(.constdata)
    .constdata                               0x0800543c   Section      944  oled_font.o(.constdata)
    .constdata                               0x080057ec   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x080057ec   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x08005828   Data          64  bigflt0.o(.constdata)
    .conststring                             0x08005880   Section      328  main.o(.conststring)
    .conststring                             0x080059c8   Section       16  signal_generator.o(.conststring)
    .conststring                             0x080059d8   Section      145  matrix_keypad.o(.conststring)
    locale$$data                             0x08005a8c   Section      272  lc_ctype_c.o(locale$$data)
    __lcctype_c_name                         0x08005a90   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x08005a98   Data           0  lc_ctype_c.o(locale$$data)
    locale$$data                             0x08005b9c   Section       28  lc_numeric_c.o(locale$$data)
    __lcctype_c_end                          0x08005b9c   Data           0  lc_ctype_c.o(locale$$data)
    __lcnum_c_name                           0x08005ba0   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x08005ba8   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x08005bb4   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x08005bb6   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x08005bb7   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x08005bb8   Data           0  lc_numeric_c.o(locale$$data)
    .data                                    0x20000000   Section        4  main.o(.data)
    .data                                    0x20000004   Section       20  system_stm32f4xx.o(.data)
    .data                                    0x20000018   Section       25  systick.o(.data)
    s_delay_counter                          0x2000002c   Data           4  systick.o(.data)
    s_dwt_initialized                        0x20000030   Data           1  systick.o(.data)
    .data                                    0x20000038   Section       26  ad9910_waveform.o(.data)
    profile_data                             0x20000038   Data           8  ad9910_waveform.o(.data)
    frequency_calibration_factor             0x20000040   Data           8  ad9910_waveform.o(.data)
    amplitude_calibration_factor             0x20000048   Data           8  ad9910_waveform.o(.data)
    temperature_offset                       0x20000050   Data           2  ad9910_waveform.o(.data)
    .data                                    0x20000058   Section       37  ad9910_control.o(.data)
    param_ranges                             0x20000058   Data          32  ad9910_control.o(.data)
    control_callback                         0x20000078   Data           4  ad9910_control.o(.data)
    system_initialized                       0x2000007c   Data           1  ad9910_control.o(.data)
    .data                                    0x2000007e   Section        2  command_parser.o(.data)
    uart_buffer_pos                          0x2000007e   Data           2  command_parser.o(.data)
    .data                                    0x20000080   Section        1  gain_calculator.o(.data)
    gain_calc_initialized                    0x20000080   Data           1  gain_calculator.o(.data)
    .data                                    0x20000084   Section       74  signal_generator.o(.data)
    s_sig_gen                                0x20000088   Data          40  signal_generator.o(.data)
    s_wave_names                             0x200000b0   Data          16  signal_generator.o(.data)
    s_last_display_update                    0x200000c0   Data           4  signal_generator.o(.data)
    s_last_key_scan                          0x200000c4   Data           4  signal_generator.o(.data)
    s_last_key_time                          0x200000c8   Data           4  signal_generator.o(.data)
    s_last_key_code                          0x200000cc   Data           1  signal_generator.o(.data)
    s_last_keypad_id                         0x200000cd   Data           1  signal_generator.o(.data)
    .data                                    0x200000d0   Section      140  matrix_keypad.o(.data)
    s_keypad1_names                          0x200000d4   Data          68  matrix_keypad.o(.data)
    s_keypad2_names                          0x20000118   Data          68  matrix_keypad.o(.data)
    .bss                                     0x2000015c   Section      512  main.o(.bss)
    debug_info                               0x2000015c   Data         512  main.o(.bss)
    .bss                                     0x2000035c   Section       16  systick.o(.bss)
    .bss                                     0x2000036c   Section       12  ad9910_waveform.o(.bss)
    current_config                           0x2000036c   Data          12  ad9910_waveform.o(.bss)
    .bss                                     0x20000378   Section      280  ad9910_control.o(.bss)
    system_params                            0x20000378   Data          24  ad9910_control.o(.bss)
    presets                                  0x20000390   Data         256  ad9910_control.o(.bss)
    .bss                                     0x20000490   Section      128  command_parser.o(.bss)
    uart_buffer                              0x20000490   Data         128  command_parser.o(.bss)
    .bss                                     0x20000510   Section       56  gain_calculator.o(.bss)
    current_stages                           0x20000510   Data          56  gain_calculator.o(.bss)
    .bss                                     0x20000548   Section       20  signal_generator.o(.bss)
    s_measurement                            0x20000548   Data          20  signal_generator.o(.bss)
    .bss                                     0x2000055c   Section     1024  oled.o(.bss)
    .bss                                     0x2000095c   Section       96  libspace.o(.bss)
    HEAP                                     0x200009c0   Section      512  startup_stm32f40_41xxx.o(HEAP)
    Heap_Mem                                 0x200009c0   Data         512  startup_stm32f40_41xxx.o(HEAP)
    STACK                                    0x20000bc0   Section     1024  startup_stm32f40_41xxx.o(STACK)
    Stack_Mem                                0x20000bc0   Data        1024  startup_stm32f40_41xxx.o(STACK)
    __initial_sp                             0x20000fc0   Data           0  startup_stm32f40_41xxx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f40_41xxx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f40_41xxx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __decompress                             0x080001c5   Thumb Code    90  __dczerorl2.o(!!dczerorl2)
    __decompress1                            0x080001c5   Thumb Code     0  __dczerorl2.o(!!dczerorl2)
    __scatterload_zeroinit                   0x08000221   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_f                                0x0800023d   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_percent                          0x0800023d   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_d                                0x08000243   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x08000249   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_s                                0x0800024f   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_percent_end                      0x08000255   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x08000259   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x0800025b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x0800025f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x0800025f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x0800025f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x0800025f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x0800025f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x08000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x08000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_lc_ctype_1                 0x08000271   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000271   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x08000271   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x0800027b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x0800027b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x0800027b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x0800027b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x0800027b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x0800027b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x0800027b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x0800027b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x0800027b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x0800027b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x0800027b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x0800027b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x0800027b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x0800027d   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x0800027f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x0800027f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x0800027f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x0800027f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x0800027f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x0800027f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x0800027f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000281   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000281   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000281   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000287   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000287   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x0800028b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800028b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000293   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000295   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000295   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000299   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080002a1   Thumb Code     8  startup_stm32f40_41xxx.o(.text)
    ADC_IRQHandler                           0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX0_IRQHandler                      0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX1_IRQHandler                      0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_SCE_IRQHandler                      0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_TX_IRQHandler                       0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX0_IRQHandler                      0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX1_IRQHandler                      0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_SCE_IRQHandler                      0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_TX_IRQHandler                       0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CRYP_IRQHandler                          0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DCMI_IRQHandler                          0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream5_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream2_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_IRQHandler                           0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_WKUP_IRQHandler                      0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI15_10_IRQHandler                     0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI1_IRQHandler                         0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI2_IRQHandler                         0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI3_IRQHandler                         0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI4_IRQHandler                         0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI9_5_IRQHandler                       0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FLASH_IRQHandler                         0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FPU_IRQHandler                           0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FSMC_IRQHandler                          0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    HASH_RNG_IRQHandler                      0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_ER_IRQHandler                       0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_EV_IRQHandler                       0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_ER_IRQHandler                       0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_EV_IRQHandler                       0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_ER_IRQHandler                       0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_EV_IRQHandler                       0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_IRQHandler                        0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_IRQHandler                        0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    PVD_IRQHandler                           0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RCC_IRQHandler                           0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_Alarm_IRQHandler                     0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_WKUP_IRQHandler                      0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SDIO_IRQHandler                          0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI1_IRQHandler                          0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI2_IRQHandler                          0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI3_IRQHandler                          0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_CC_IRQHandler                       0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM3_IRQHandler                          0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM4_IRQHandler                          0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM5_IRQHandler                          0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM7_IRQHandler                          0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_CC_IRQHandler                       0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART4_IRQHandler                         0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART5_IRQHandler                         0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART1_IRQHandler                        0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART3_IRQHandler                        0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART6_IRQHandler                        0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    WWDG_IRQHandler                          0x080002bb   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __user_initial_stackheap                 0x080002bd   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __2sprintf                               0x080002e1   Thumb Code    38  __2sprintf.o(.text)
    _printf_str                              0x0800030d   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x08000361   Thumb Code   104  _printf_dec.o(.text)
    __printf                                 0x080003d9   Thumb Code   352  __printf_ss_wp.o(.text)
    atoi                                     0x08000539   Thumb Code    26  atoi.o(.text)
    atol                                     0x08000553   Thumb Code    26  atol.o(.text)
    strcpy                                   0x0800056d   Thumb Code    72  strcpy.o(.text)
    strlen                                   0x080005b5   Thumb Code    62  strlen.o(.text)
    __aeabi_memclr                           0x080005f3   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x080005f3   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x080005f7   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x08000637   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x08000637   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x08000637   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x0800063b   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x08000685   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000687   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08000689   Thumb Code     2  heapauxi.o(.text)
    __rt_ctype_table                         0x0800068d   Thumb Code    16  rt_ctype_table.o(.text)
    __aeabi_errno_addr                       0x0800069d   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x0800069d   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x0800069d   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __read_errno                             0x080006a5   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x080006af   Thumb Code    12  _rserrno.o(.text)
    _printf_int_common                       0x080006bb   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x0800076d   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x0800091f   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_common                      0x08000b97   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000bbd   Thumb Code    10  _sputc.o(.text)
    _printf_cs_common                        0x08000bc7   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08000bdb   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08000beb   Thumb Code     8  _printf_char.o(.text)
    _strtoul                                 0x08000bf3   Thumb Code   158  _strtoul.o(.text)
    strtol                                   0x08000c91   Thumb Code   112  strtol.o(.text)
    __user_libspace                          0x08000d01   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000d01   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000d01   Thumb Code     0  libspace.o(.text)
    __rt_locale                              0x08000d09   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _ll_udiv10                               0x08000d11   Thumb Code   138  lludiv10.o(.text)
    _printf_fp_infnan                        0x08000d9d   Thumb Code   112  _printf_fp_infnan.o(.text)
    _chval                                   0x08000e1d   Thumb Code    28  _chval.o(.text)
    _btod_etento                             0x08000e39   Thumb Code   224  bigflt0.o(.text)
    __user_setup_stackheap                   0x08000f1d   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08000f67   Thumb Code    18  exit.o(.text)
    strcmp                                   0x08000f79   Thumb Code   128  strcmpv7m.o(.text)
    _sys_exit                                0x08000ff9   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x08001005   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08001005   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x08001007   Thumb Code     0  indicate_semi.o(.text)
    _btod_d2e                                0x08001007   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08001045   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x0800108b   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x080010eb   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x08001423   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x080014ff   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08001529   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x08001553   Thumb Code   580  btod.o(CL$$btod_mult_common)
    AD9910_Control_CalculateActualOutput     0x080019dd   Thumb Code   130  ad9910_control.o(i.AD9910_Control_CalculateActualOutput)
    AD9910_Control_CalculateTargetOutput     0x08001a79   Thumb Code    70  ad9910_control.o(i.AD9910_Control_CalculateTargetOutput)
    AD9910_Control_EnableOutput              0x08001ac9   Thumb Code    28  ad9910_control.o(i.AD9910_Control_EnableOutput)
    AD9910_Control_Init                      0x08001ae9   Thumb Code   156  ad9910_control.o(i.AD9910_Control_Init)
    AD9910_Control_RegisterCallback          0x08001bc5   Thumb Code     6  ad9910_control.o(i.AD9910_Control_RegisterCallback)
    AD9910_Control_SetFrequency              0x08001bd1   Thumb Code    36  ad9910_control.o(i.AD9910_Control_SetFrequency)
    AD9910_Control_SetGainFactor             0x08001bf9   Thumb Code   120  ad9910_control.o(i.AD9910_Control_SetGainFactor)
    AD9910_Control_SetTargetAmplitude        0x08001c75   Thumb Code    70  ad9910_control.o(i.AD9910_Control_SetTargetAmplitude)
    AD9910_Control_Task                      0x08001cc1   Thumb Code     2  ad9910_control.o(i.AD9910_Control_Task)
    AD9910_DisableOutput                     0x08001cc5   Thumb Code    14  ad9910_waveform.o(i.AD9910_DisableOutput)
    AD9910_EnableInverseSincFilter           0x08001cd9   Thumb Code    68  ad9910_waveform.o(i.AD9910_EnableInverseSincFilter)
    AD9910_EnableOutput                      0x08001d21   Thumb Code    14  ad9910_waveform.o(i.AD9910_EnableOutput)
    AD9910_EnsurePhaseContinuity             0x08001d35   Thumb Code    50  ad9910_waveform.o(i.AD9910_EnsurePhaseContinuity)
    AD9910_HAL_DelayMs                       0x08001d6d   Thumb Code    12  ad9910_hal.o(i.AD9910_HAL_DelayMs)
    AD9910_HAL_DelayUs                       0x08001d79   Thumb Code    24  ad9910_hal.o(i.AD9910_HAL_DelayUs)
    AD9910_HAL_IOUpdate                      0x08001d91   Thumb Code    26  ad9910_hal.o(i.AD9910_HAL_IOUpdate)
    AD9910_HAL_Init                          0x08001db1   Thumb Code   204  ad9910_hal.o(i.AD9910_HAL_Init)
    AD9910_HAL_PowerControl                  0x08001e89   Thumb Code    30  ad9910_hal.o(i.AD9910_HAL_PowerControl)
    AD9910_HAL_Reset                         0x08001ead   Thumb Code    32  ad9910_hal.o(i.AD9910_HAL_Reset)
    AD9910_HAL_SelectProfile                 0x08001ed1   Thumb Code    82  ad9910_hal.o(i.AD9910_HAL_SelectProfile)
    AD9910_HAL_Send8Bits                     0x08001f29   Thumb Code    70  ad9910_hal.o(i.AD9910_HAL_Send8Bits)
    AD9910_Init                              0x08001f75   Thumb Code   210  ad9910_waveform.o(i.AD9910_Init)
    AD9910_OptimizeOutputFilter              0x08002061   Thumb Code     8  ad9910_waveform.o(i.AD9910_OptimizeOutputFilter)
    AD9910_OptimizeSmoothness                0x08002069   Thumb Code    26  ad9910_waveform.o(i.AD9910_OptimizeSmoothness)
    AD9910_OptimizeWaveform                  0x08002085   Thumb Code    40  ad9910_waveform.o(i.AD9910_OptimizeWaveform)
    AD9910_Set5MHz_HighQuality               0x080020d1   Thumb Code    34  ad9910_waveform.o(i.AD9910_Set5MHz_HighQuality)
    AD9910_SetAmplitude_Precision            0x080020f9   Thumb Code    44  ad9910_waveform.o(i.AD9910_SetAmplitude_Precision)
    AD9910_SetFrequency_Precision            0x0800212d   Thumb Code    52  ad9910_waveform.o(i.AD9910_SetFrequency_Precision)
    AD9910_WriteRegister                     0x080021ad   Thumb Code    72  ad9910_waveform.o(i.AD9910_WriteRegister)
    BSP_Init                                 0x080021f9   Thumb Code    44  bsp.o(i.BSP_Init)
    BusFault_Handler                         0x08002225   Thumb Code     4  stm32f4xx_it.o(i.BusFault_Handler)
    CommandParser_Init                       0x08002229   Thumb Code    18  command_parser.o(i.CommandParser_Init)
    ControlCallback                          0x08002245   Thumb Code     2  main.o(i.ControlCallback)
    DWT_Init                                 0x08002249   Thumb Code    78  systick.o(i.DWT_Init)
    DebugMon_Handler                         0x080022a5   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Delay_ms                                 0x080022a9   Thumb Code    40  systick.o(i.Delay_ms)
    Delay_us                                 0x080022d5   Thumb Code    68  systick.o(i.Delay_us)
    EXTI0_IRQHandler                         0x08002325   Thumb Code     8  stm32f4xx_it.o(i.EXTI0_IRQHandler)
    EXTI0_IRQHandler_Internal                0x0800232d   Thumb Code     2  main.o(i.EXTI0_IRQHandler_Internal)
    FLASH_SetLatency                         0x08002331   Thumb Code    84  stm32f4xx_flash.o(i.FLASH_SetLatency)
    GPIO_Init                                0x080023a5   Thumb Code   352  stm32f4xx_gpio.o(i.GPIO_Init)
    GPIO_ReadInputDataBit                    0x0800254d   Thumb Code   194  stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit)
    GPIO_ResetBits                           0x08002659   Thumb Code   104  stm32f4xx_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x08002709   Thumb Code   104  stm32f4xx_gpio.o(i.GPIO_SetBits)
    GainCalculator_GetAD9910Output           0x080027b9   Thumb Code   152  gain_calculator.o(i.GainCalculator_GetAD9910Output)
    GainCalculator_GetFinalOutput            0x08002871   Thumb Code    70  gain_calculator.o(i.GainCalculator_GetFinalOutput)
    GainCalculator_Init                      0x080028c1   Thumb Code    66  gain_calculator.o(i.GainCalculator_Init)
    GainCalculator_TransferFunctionGain      0x08002925   Thumb Code   204  gain_calculator.o(i.GainCalculator_TransferFunctionGain)
    HardFault_Handler                        0x08002a19   Thumb Code     4  stm32f4xx_it.o(i.HardFault_Handler)
    IIC_Send_Byte                            0x08002a1d   Thumb Code    86  oled.o(i.IIC_Send_Byte)
    IIC_Start                                0x08002a79   Thumb Code    48  oled.o(i.IIC_Start)
    IIC_Stop                                 0x08002aad   Thumb Code    54  oled.o(i.IIC_Stop)
    IIC_Wait_Ack                             0x08002ae9   Thumb Code    74  oled.o(i.IIC_Wait_Ack)
    MatrixKeypad_Init                        0x08002bd9   Thumb Code    26  matrix_keypad.o(i.MatrixKeypad_Init)
    MatrixKeypad_ScanAll                     0x08002c35   Thumb Code    78  matrix_keypad.o(i.MatrixKeypad_ScanAll)
    MemManage_Handler                        0x08002d99   Thumb Code     4  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08002d9d   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    OLED_Clear                               0x08002dc9   Thumb Code    42  oled.o(i.OLED_Clear)
    OLED_GetCharFont                         0x08002df9   Thumb Code    26  oled_font.o(i.OLED_GetCharFont)
    OLED_Init                                0x08002e19   Thumb Code   314  oled.o(i.OLED_Init)
    OLED_Refresh_Gram                        0x08002f59   Thumb Code    70  oled.o(i.OLED_Refresh_Gram)
    OLED_Set_Pos                             0x08002fa5   Thumb Code    42  oled.o(i.OLED_Set_Pos)
    OLED_ShowChar                            0x08002fd1   Thumb Code   152  oled.o(i.OLED_ShowChar)
    OLED_ShowString                          0x0800306d   Thumb Code    54  oled.o(i.OLED_ShowString)
    OLED_WR_Byte                             0x080030a3   Thumb Code    56  oled.o(i.OLED_WR_Byte)
    PendSV_Handler                           0x080030db   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    PrintGainCalculationDetails              0x080030dd   Thumb Code   214  main.o(i.PrintGainCalculationDetails)
    RCC_AHB1PeriphClockCmd                   0x08003205   Thumb Code    70  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x0800326d   Thumb Code    70  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_DeInit                               0x080032d5   Thumb Code    82  stm32f4xx_rcc.o(i.RCC_DeInit)
    RCC_GetFlagStatus                        0x08003339   Thumb Code   134  stm32f4xx_rcc.o(i.RCC_GetFlagStatus)
    RCC_GetSYSCLKSource                      0x080033dd   Thumb Code    10  stm32f4xx_rcc.o(i.RCC_GetSYSCLKSource)
    RCC_HCLKConfig                           0x080033ed   Thumb Code    66  stm32f4xx_rcc.o(i.RCC_HCLKConfig)
    RCC_HSEConfig                            0x0800344d   Thumb Code    38  stm32f4xx_rcc.o(i.RCC_HSEConfig)
    RCC_PCLK1Config                          0x08003491   Thumb Code    58  stm32f4xx_rcc.o(i.RCC_PCLK1Config)
    RCC_PCLK2Config                          0x080034e9   Thumb Code    60  stm32f4xx_rcc.o(i.RCC_PCLK2Config)
    RCC_PLLCmd                               0x08003541   Thumb Code    28  stm32f4xx_rcc.o(i.RCC_PLLCmd)
    RCC_PLLConfig                            0x08003579   Thumb Code   154  stm32f4xx_rcc.o(i.RCC_PLLConfig)
    RCC_SYSCLKConfig                         0x08003631   Thumb Code    42  stm32f4xx_rcc.o(i.RCC_SYSCLKConfig)
    RCC_WaitForHSEStartUp                    0x08003679   Thumb Code    56  stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp)
    SVC_Handler                              0x080036b1   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SignalGenerator_Init                     0x08003b71   Thumb Code    50  signal_generator.o(i.SignalGenerator_Init)
    SignalGenerator_Process                  0x08003bf1   Thumb Code   298  signal_generator.o(i.SignalGenerator_Process)
    SignalGenerator_SetAmplitude             0x08003f91   Thumb Code    46  signal_generator.o(i.SignalGenerator_SetAmplitude)
    SignalGenerator_SetFrequency             0x08003fc5   Thumb Code    42  signal_generator.o(i.SignalGenerator_SetFrequency)
    SignalGenerator_SetWaveType              0x08003ff9   Thumb Code    38  signal_generator.o(i.SignalGenerator_SetWaveType)
    SignalGenerator_UpdateDisplay            0x08004025   Thumb Code    78  signal_generator.o(i.SignalGenerator_UpdateDisplay)
    SysTick_GetTick                          0x0800410d   Thumb Code     6  systick.o(i.SysTick_GetTick)
    SysTick_Handler                          0x08004119   Thumb Code    22  stm32f4xx_it.o(i.SysTick_Handler)
    SysTick_Handler_Internal                 0x08004135   Thumb Code    42  systick.o(i.SysTick_Handler_Internal)
    SysTick_Init                             0x0800416d   Thumb Code   114  systick.o(i.SysTick_Init)
    SysTick_ResetStats                       0x080041e9   Thumb Code    22  systick.o(i.SysTick_ResetStats)
    SystemClock_Config                       0x08004269   Thumb Code   106  main.o(i.SystemClock_Config)
    SystemInit                               0x080042d5   Thumb Code    88  system_stm32f4xx.o(i.SystemInit)
    TIM2_IRQHandler                          0x0800433d   Thumb Code    22  stm32f4xx_it.o(i.TIM2_IRQHandler)
    TIM6_DAC_IRQHandler                      0x08004353   Thumb Code     2  stm32f4xx_it.o(i.TIM6_DAC_IRQHandler)
    TIM_ClearITPendingBit                    0x08004355   Thumb Code   108  stm32f4xx_tim.o(i.TIM_ClearITPendingBit)
    TIM_GetITStatus                          0x0800440d   Thumb Code   186  stm32f4xx_tim.o(i.TIM_GetITStatus)
    TimingDelay_Decrement                    0x08004515   Thumb Code     2  main.o(i.TimingDelay_Decrement)
    USART2_IRQHandler                        0x08004517   Thumb Code     2  stm32f4xx_it.o(i.USART2_IRQHandler)
    UsageFault_Handler                       0x08004529   Thumb Code     4  stm32f4xx_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x080045c5   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __hardfp_sqrt                            0x080045f5   Thumb Code   122  sqrt.o(i.__hardfp_sqrt)
    _is_digit                                0x0800466f   Thumb Code    14  __printf_wp.o(i._is_digit)
    assert_failed                            0x0800467d   Thumb Code     4  main.o(i.assert_failed)
    main                                     0x08004681   Thumb Code   188  main.o(i.main)
    _get_lc_ctype                            0x0800477d   Thumb Code    44  lc_ctype_c.o(locale$$code)
    _get_lc_numeric                          0x080047a9   Thumb Code    44  lc_numeric_c.o(locale$$code)
    __aeabi_dadd                             0x080047d5   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x080047d5   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcmp_Inf                           0x08004925   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_ddiv                             0x0800493d   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x0800493d   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_d2uiz                            0x08004bed   Thumb Code     0  dfixu.o(x$fpl$dfixu)
    _dfixu                                   0x08004bed   Thumb Code    90  dfixu.o(x$fpl$dfixu)
    __aeabi_i2d                              0x08004c47   Thumb Code     0  dflt_clz.o(x$fpl$dflt)
    _dflt                                    0x08004c47   Thumb Code    46  dflt_clz.o(x$fpl$dflt)
    __aeabi_ui2d                             0x08004c75   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x08004c75   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_cdcmple                          0x08004c9d   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    _dcmple                                  0x08004c9d   Thumb Code   120  dleqf.o(x$fpl$dleqf)
    __fpl_dcmple_InfNaN                      0x08004cff   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    __aeabi_dmul                             0x08004d15   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08004d15   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x08004e69   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08004f05   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_cdrcmple                         0x08004f11   Thumb Code     0  drleqf.o(x$fpl$drleqf)
    _drcmple                                 0x08004f11   Thumb Code   108  drleqf.o(x$fpl$drleqf)
    __aeabi_drsub                            0x08004f7d   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x08004f7d   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    _dsqrt                                   0x08004f95   Thumb Code   404  dsqrt_umaal.o(x$fpl$dsqrt)
    __aeabi_dsub                             0x0800512d   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x0800512d   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_f2d                              0x08005301   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08005301   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x08005357   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x080053e3   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x080053eb   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x080053eb   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    _printf_fp_dec                           0x080053ed   Thumb Code     4  printf1.o(x$fpl$printf1)
    __I$use$fp                               0x080053f0   Number         0  usenofp.o(x$fpl$usenofp)
    F8X16                                    0x0800543c   Data         944  oled_font.o(.constdata)
    Region$$Table$$Base                      0x08005a6c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08005a8c   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x08005a99   Data           0  lc_ctype_c.o(locale$$data)
    uwTick                                   0x20000000   Data           4  main.o(.data)
    SystemCoreClock                          0x20000004   Data           4  system_stm32f4xx.o(.data)
    AHBPrescTable                            0x20000008   Data          16  system_stm32f4xx.o(.data)
    g_systick_counter                        0x20000018   Data           4  systick.o(.data)
    g_system_uptime_ms                       0x2000001c   Data           4  systick.o(.data)
    g_systick_cal                            0x20000020   Data          12  systick.o(.data)
    g_signal_generator_initialized           0x20000084   Data           1  signal_generator.o(.data)
    g_keypad_initialized                     0x200000d0   Data           1  matrix_keypad.o(.data)
    g_systick_stats                          0x2000035c   Data          16  systick.o(.bss)
    OLED_GRAM                                0x2000055c   Data        1024  oled.o(.bss)
    __libspace_start                         0x2000095c   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200009bc   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00005d14, Max: 0x00100000, ABSOLUTE, COMPRESSED[0x00005c64])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00005bb8, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f40_41xxx.o
    0x08000188   0x08000188   0x00000008   Code   RO         6909  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         7354    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000005a   Code   RO         7352    !!dczerorl2         c_w.l(__dczerorl2.o)
    0x0800021e   0x0800021e   0x00000002   PAD
    0x08000220   0x08000220   0x0000001c   Code   RO         7356    !!handler_zi        c_w.l(__scatter_zi.o)
    0x0800023c   0x0800023c   0x00000000   Code   RO         6884    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x0800023c   0x0800023c   0x00000006   Code   RO         6883    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x08000242   0x08000242   0x00000006   Code   RO         6881    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000248   0x08000248   0x00000006   Code   RO         6882    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x0800024e   0x0800024e   0x00000006   Code   RO         6880    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x08000254   0x08000254   0x00000004   Code   RO         7013    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x08000258   0x08000258   0x00000002   Code   RO         7167    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x0800025a   0x0800025a   0x00000004   Code   RO         7168    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x0800025e   0x0800025e   0x00000000   Code   RO         7171    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x0800025e   0x0800025e   0x00000000   Code   RO         7174    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x0800025e   0x0800025e   0x00000000   Code   RO         7176    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x0800025e   0x0800025e   0x00000000   Code   RO         7178    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x0800025e   0x0800025e   0x00000006   Code   RO         7179    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x08000264   0x08000264   0x00000000   Code   RO         7181    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000264   0x08000264   0x0000000c   Code   RO         7182    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x08000270   0x08000270   0x00000000   Code   RO         7183    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000270   0x08000270   0x00000000   Code   RO         7185    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000270   0x08000270   0x0000000a   Code   RO         7186    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x0800027a   0x0800027a   0x00000000   Code   RO         7187    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x0800027a   0x0800027a   0x00000000   Code   RO         7189    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x0800027a   0x0800027a   0x00000000   Code   RO         7191    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x0800027a   0x0800027a   0x00000000   Code   RO         7193    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x0800027a   0x0800027a   0x00000000   Code   RO         7195    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x0800027a   0x0800027a   0x00000000   Code   RO         7197    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x0800027a   0x0800027a   0x00000000   Code   RO         7199    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x0800027a   0x0800027a   0x00000000   Code   RO         7201    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x0800027a   0x0800027a   0x00000000   Code   RO         7205    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x0800027a   0x0800027a   0x00000000   Code   RO         7207    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x0800027a   0x0800027a   0x00000000   Code   RO         7209    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x0800027a   0x0800027a   0x00000000   Code   RO         7211    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x0800027a   0x0800027a   0x00000002   Code   RO         7212    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x0800027c   0x0800027c   0x00000002   Code   RO         7274    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x0800027e   0x0800027e   0x00000000   Code   RO         7303    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x0800027e   0x0800027e   0x00000000   Code   RO         7305    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x0800027e   0x0800027e   0x00000000   Code   RO         7308    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x0800027e   0x0800027e   0x00000000   Code   RO         7311    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x0800027e   0x0800027e   0x00000000   Code   RO         7313    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x0800027e   0x0800027e   0x00000000   Code   RO         7316    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x0800027e   0x0800027e   0x00000002   Code   RO         7317    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x08000280   0x08000280   0x00000000   Code   RO         6991    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000280   0x08000280   0x00000000   Code   RO         7082    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000280   0x08000280   0x00000006   Code   RO         7094    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x08000286   0x08000286   0x00000000   Code   RO         7084    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x08000286   0x08000286   0x00000004   Code   RO         7085    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x0800028a   0x0800028a   0x00000000   Code   RO         7087    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x0800028a   0x0800028a   0x00000008   Code   RO         7088    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000292   0x08000292   0x00000002   Code   RO         7213    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000294   0x08000294   0x00000000   Code   RO         7238    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000294   0x08000294   0x00000004   Code   RO         7239    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000298   0x08000298   0x00000006   Code   RO         7240    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x0800029e   0x0800029e   0x00000002   PAD
    0x080002a0   0x080002a0   0x00000040   Code   RO            4    .text               startup_stm32f40_41xxx.o
    0x080002e0   0x080002e0   0x0000002c   Code   RO         6846    .text               c_w.l(__2sprintf.o)
    0x0800030c   0x0800030c   0x00000052   Code   RO         6856    .text               c_w.l(_printf_str.o)
    0x0800035e   0x0800035e   0x00000002   PAD
    0x08000360   0x08000360   0x00000078   Code   RO         6858    .text               c_w.l(_printf_dec.o)
    0x080003d8   0x080003d8   0x00000160   Code   RO         6875    .text               c_w.l(__printf_ss_wp.o)
    0x08000538   0x08000538   0x0000001a   Code   RO         6885    .text               c_w.l(atoi.o)
    0x08000552   0x08000552   0x0000001a   Code   RO         6887    .text               c_w.l(atol.o)
    0x0800056c   0x0800056c   0x00000048   Code   RO         6893    .text               c_w.l(strcpy.o)
    0x080005b4   0x080005b4   0x0000003e   Code   RO         6895    .text               c_w.l(strlen.o)
    0x080005f2   0x080005f2   0x00000044   Code   RO         6903    .text               c_w.l(rt_memclr.o)
    0x08000636   0x08000636   0x0000004e   Code   RO         6905    .text               c_w.l(rt_memclr_w.o)
    0x08000684   0x08000684   0x00000006   Code   RO         6907    .text               c_w.l(heapauxi.o)
    0x0800068a   0x0800068a   0x00000002   PAD
    0x0800068c   0x0800068c   0x00000010   Code   RO         6992    .text               c_w.l(rt_ctype_table.o)
    0x0800069c   0x0800069c   0x00000008   Code   RO         6997    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x080006a4   0x080006a4   0x00000016   Code   RO         6999    .text               c_w.l(_rserrno.o)
    0x080006ba   0x080006ba   0x000000b2   Code   RO         7001    .text               c_w.l(_printf_intcommon.o)
    0x0800076c   0x0800076c   0x0000041e   Code   RO         7003    .text               c_w.l(_printf_fp_dec.o)
    0x08000b8a   0x08000b8a   0x00000002   PAD
    0x08000b8c   0x08000b8c   0x00000030   Code   RO         7005    .text               c_w.l(_printf_char_common.o)
    0x08000bbc   0x08000bbc   0x0000000a   Code   RO         7007    .text               c_w.l(_sputc.o)
    0x08000bc6   0x08000bc6   0x0000002c   Code   RO         7011    .text               c_w.l(_printf_char.o)
    0x08000bf2   0x08000bf2   0x0000009e   Code   RO         7014    .text               c_w.l(_strtoul.o)
    0x08000c90   0x08000c90   0x00000070   Code   RO         7018    .text               c_w.l(strtol.o)
    0x08000d00   0x08000d00   0x00000008   Code   RO         7078    .text               c_w.l(libspace.o)
    0x08000d08   0x08000d08   0x00000008   Code   RO         7099    .text               c_w.l(rt_locale_intlibspace.o)
    0x08000d10   0x08000d10   0x0000008a   Code   RO         7101    .text               c_w.l(lludiv10.o)
    0x08000d9a   0x08000d9a   0x00000002   PAD
    0x08000d9c   0x08000d9c   0x00000080   Code   RO         7105    .text               c_w.l(_printf_fp_infnan.o)
    0x08000e1c   0x08000e1c   0x0000001c   Code   RO         7109    .text               c_w.l(_chval.o)
    0x08000e38   0x08000e38   0x000000e4   Code   RO         7113    .text               c_w.l(bigflt0.o)
    0x08000f1c   0x08000f1c   0x0000004a   Code   RO         7154    .text               c_w.l(sys_stackheap_outer.o)
    0x08000f66   0x08000f66   0x00000012   Code   RO         7158    .text               c_w.l(exit.o)
    0x08000f78   0x08000f78   0x00000080   Code   RO         7160    .text               c_w.l(strcmpv7m.o)
    0x08000ff8   0x08000ff8   0x0000000c   Code   RO         7262    .text               c_w.l(sys_exit.o)
    0x08001004   0x08001004   0x00000002   Code   RO         7293    .text               c_w.l(use_no_semi.o)
    0x08001006   0x08001006   0x00000000   Code   RO         7295    .text               c_w.l(indicate_semi.o)
    0x08001006   0x08001006   0x0000003e   Code   RO         7116    CL$$btod_d2e        c_w.l(btod.o)
    0x08001044   0x08001044   0x00000046   Code   RO         7118    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x0800108a   0x0800108a   0x00000060   Code   RO         7117    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x080010ea   0x080010ea   0x00000338   Code   RO         7126    CL$$btod_div_common  c_w.l(btod.o)
    0x08001422   0x08001422   0x000000dc   Code   RO         7123    CL$$btod_e2e        c_w.l(btod.o)
    0x080014fe   0x080014fe   0x0000002a   Code   RO         7120    CL$$btod_ediv       c_w.l(btod.o)
    0x08001528   0x08001528   0x0000002a   Code   RO         7119    CL$$btod_emul       c_w.l(btod.o)
    0x08001552   0x08001552   0x00000244   Code   RO         7125    CL$$btod_mult_common  c_w.l(btod.o)
    0x08001796   0x08001796   0x00000002   PAD
    0x08001798   0x08001798   0x00000168   Code   RO         5732    i.AD9910_CalculateAmplitudeWord_Precision  ad9910_waveform.o
    0x08001900   0x08001900   0x000000dc   Code   RO         5734    i.AD9910_CalculateFrequencyWord_Precision  ad9910_waveform.o
    0x080019dc   0x080019dc   0x0000009c   Code   RO         5922    i.AD9910_Control_CalculateActualOutput  ad9910_control.o
    0x08001a78   0x08001a78   0x00000050   Code   RO         5923    i.AD9910_Control_CalculateTargetOutput  ad9910_control.o
    0x08001ac8   0x08001ac8   0x00000020   Code   RO         5924    i.AD9910_Control_EnableOutput  ad9910_control.o
    0x08001ae8   0x08001ae8   0x000000dc   Code   RO         5928    i.AD9910_Control_Init  ad9910_control.o
    0x08001bc4   0x08001bc4   0x0000000c   Code   RO         5930    i.AD9910_Control_RegisterCallback  ad9910_control.o
    0x08001bd0   0x08001bd0   0x00000028   Code   RO         5931    i.AD9910_Control_SetFrequency  ad9910_control.o
    0x08001bf8   0x08001bf8   0x0000007c   Code   RO         5932    i.AD9910_Control_SetGainFactor  ad9910_control.o
    0x08001c74   0x08001c74   0x0000004c   Code   RO         5934    i.AD9910_Control_SetTargetAmplitude  ad9910_control.o
    0x08001cc0   0x08001cc0   0x00000002   Code   RO         5935    i.AD9910_Control_Task  ad9910_control.o
    0x08001cc2   0x08001cc2   0x00000002   PAD
    0x08001cc4   0x08001cc4   0x00000014   Code   RO         5736    i.AD9910_DisableOutput  ad9910_waveform.o
    0x08001cd8   0x08001cd8   0x00000048   Code   RO         5737    i.AD9910_EnableInverseSincFilter  ad9910_waveform.o
    0x08001d20   0x08001d20   0x00000014   Code   RO         5738    i.AD9910_EnableOutput  ad9910_waveform.o
    0x08001d34   0x08001d34   0x00000038   Code   RO         5739    i.AD9910_EnsurePhaseContinuity  ad9910_waveform.o
    0x08001d6c   0x08001d6c   0x0000000c   Code   RO         5658    i.AD9910_HAL_DelayMs  ad9910_hal.o
    0x08001d78   0x08001d78   0x00000018   Code   RO         5659    i.AD9910_HAL_DelayUs  ad9910_hal.o
    0x08001d90   0x08001d90   0x00000020   Code   RO         5660    i.AD9910_HAL_IOUpdate  ad9910_hal.o
    0x08001db0   0x08001db0   0x000000d8   Code   RO         5661    i.AD9910_HAL_Init   ad9910_hal.o
    0x08001e88   0x08001e88   0x00000024   Code   RO         5662    i.AD9910_HAL_PowerControl  ad9910_hal.o
    0x08001eac   0x08001eac   0x00000024   Code   RO         5663    i.AD9910_HAL_Reset  ad9910_hal.o
    0x08001ed0   0x08001ed0   0x00000058   Code   RO         5664    i.AD9910_HAL_SelectProfile  ad9910_hal.o
    0x08001f28   0x08001f28   0x0000004c   Code   RO         5665    i.AD9910_HAL_Send8Bits  ad9910_hal.o
    0x08001f74   0x08001f74   0x000000ec   Code   RO         5741    i.AD9910_Init       ad9910_waveform.o
    0x08002060   0x08002060   0x00000008   Code   RO         5742    i.AD9910_OptimizeOutputFilter  ad9910_waveform.o
    0x08002068   0x08002068   0x0000001a   Code   RO         5743    i.AD9910_OptimizeSmoothness  ad9910_waveform.o
    0x08002082   0x08002082   0x00000002   PAD
    0x08002084   0x08002084   0x0000004c   Code   RO         5744    i.AD9910_OptimizeWaveform  ad9910_waveform.o
    0x080020d0   0x080020d0   0x00000028   Code   RO         5746    i.AD9910_Set5MHz_HighQuality  ad9910_waveform.o
    0x080020f8   0x080020f8   0x00000034   Code   RO         5749    i.AD9910_SetAmplitude_Precision  ad9910_waveform.o
    0x0800212c   0x0800212c   0x00000040   Code   RO         5752    i.AD9910_SetFrequency_Precision  ad9910_waveform.o
    0x0800216c   0x0800216c   0x00000040   Code   RO         5757    i.AD9910_WriteProfile  ad9910_waveform.o
    0x080021ac   0x080021ac   0x0000004c   Code   RO         5758    i.AD9910_WriteRegister  ad9910_waveform.o
    0x080021f8   0x080021f8   0x0000002c   Code   RO          387    i.BSP_Init          bsp.o
    0x08002224   0x08002224   0x00000004   Code   RO          239    i.BusFault_Handler  stm32f4xx_it.o
    0x08002228   0x08002228   0x0000001c   Code   RO         6071    i.CommandParser_Init  command_parser.o
    0x08002244   0x08002244   0x00000002   Code   RO           13    i.ControlCallback   main.o
    0x08002246   0x08002246   0x00000002   PAD
    0x08002248   0x08002248   0x0000005c   Code   RO         5520    i.DWT_Init          systick.o
    0x080022a4   0x080022a4   0x00000002   Code   RO          240    i.DebugMon_Handler  stm32f4xx_it.o
    0x080022a6   0x080022a6   0x00000002   PAD
    0x080022a8   0x080022a8   0x0000002c   Code   RO         5521    i.Delay_ms          systick.o
    0x080022d4   0x080022d4   0x00000050   Code   RO         5523    i.Delay_us          systick.o
    0x08002324   0x08002324   0x00000008   Code   RO          241    i.EXTI0_IRQHandler  stm32f4xx_it.o
    0x0800232c   0x0800232c   0x00000002   Code   RO           14    i.EXTI0_IRQHandler_Internal  main.o
    0x0800232e   0x0800232e   0x00000002   PAD
    0x08002330   0x08002330   0x00000074   Code   RO         1934    i.FLASH_SetLatency  stm32f4xx_flash.o
    0x080023a4   0x080023a4   0x000001a8   Code   RO         2351    i.GPIO_Init         stm32f4xx_gpio.o
    0x0800254c   0x0800254c   0x0000010c   Code   RO         2355    i.GPIO_ReadInputDataBit  stm32f4xx_gpio.o
    0x08002658   0x08002658   0x000000b0   Code   RO         2358    i.GPIO_ResetBits    stm32f4xx_gpio.o
    0x08002708   0x08002708   0x000000b0   Code   RO         2359    i.GPIO_SetBits      stm32f4xx_gpio.o
    0x080027b8   0x080027b8   0x000000b8   Code   RO         6161    i.GainCalculator_GetAD9910Output  gain_calculator.o
    0x08002870   0x08002870   0x00000050   Code   RO         6162    i.GainCalculator_GetFinalOutput  gain_calculator.o
    0x080028c0   0x080028c0   0x00000064   Code   RO         6164    i.GainCalculator_Init  gain_calculator.o
    0x08002924   0x08002924   0x000000f4   Code   RO         6171    i.GainCalculator_TransferFunctionGain  gain_calculator.o
    0x08002a18   0x08002a18   0x00000004   Code   RO          242    i.HardFault_Handler  stm32f4xx_it.o
    0x08002a1c   0x08002a1c   0x0000005c   Code   RO         6632    i.IIC_Send_Byte     oled.o
    0x08002a78   0x08002a78   0x00000034   Code   RO         6633    i.IIC_Start         oled.o
    0x08002aac   0x08002aac   0x0000003c   Code   RO         6634    i.IIC_Stop          oled.o
    0x08002ae8   0x08002ae8   0x00000050   Code   RO         6635    i.IIC_Wait_Ack      oled.o
    0x08002b38   0x08002b38   0x000000a0   Code   RO         6458    i.MatrixKeypad_GPIO_Init  matrix_keypad.o
    0x08002bd8   0x08002bd8   0x00000020   Code   RO         6460    i.MatrixKeypad_Init  matrix_keypad.o
    0x08002bf8   0x08002bf8   0x0000003c   Code   RO         6462    i.MatrixKeypad_ReadColumn  matrix_keypad.o
    0x08002c34   0x08002c34   0x00000054   Code   RO         6464    i.MatrixKeypad_ScanAll  matrix_keypad.o
    0x08002c88   0x08002c88   0x000000ce   Code   RO         6465    i.MatrixKeypad_ScanSingle  matrix_keypad.o
    0x08002d56   0x08002d56   0x00000002   PAD
    0x08002d58   0x08002d58   0x00000040   Code   RO         6466    i.MatrixKeypad_SetRowOutput  matrix_keypad.o
    0x08002d98   0x08002d98   0x00000004   Code   RO          243    i.MemManage_Handler  stm32f4xx_it.o
    0x08002d9c   0x08002d9c   0x00000002   Code   RO          244    i.NMI_Handler       stm32f4xx_it.o
    0x08002d9e   0x08002d9e   0x00000002   PAD
    0x08002da0   0x08002da0   0x00000028   Code   RO         5524    i.NVIC_SetPriority  systick.o
    0x08002dc8   0x08002dc8   0x00000030   Code   RO         6636    i.OLED_Clear        oled.o
    0x08002df8   0x08002df8   0x00000020   Code   RO         6817    i.OLED_GetCharFont  oled_font.o
    0x08002e18   0x08002e18   0x00000140   Code   RO         6644    i.OLED_Init         oled.o
    0x08002f58   0x08002f58   0x0000004c   Code   RO         6645    i.OLED_Refresh_Gram  oled.o
    0x08002fa4   0x08002fa4   0x0000002a   Code   RO         6646    i.OLED_Set_Pos      oled.o
    0x08002fce   0x08002fce   0x00000002   PAD
    0x08002fd0   0x08002fd0   0x0000009c   Code   RO         6648    i.OLED_ShowChar     oled.o
    0x0800306c   0x0800306c   0x00000036   Code   RO         6650    i.OLED_ShowString   oled.o
    0x080030a2   0x080030a2   0x00000038   Code   RO         6653    i.OLED_WR_Byte      oled.o
    0x080030da   0x080030da   0x00000002   Code   RO          245    i.PendSV_Handler    stm32f4xx_it.o
    0x080030dc   0x080030dc   0x00000128   Code   RO           15    i.PrintGainCalculationDetails  main.o
    0x08003204   0x08003204   0x00000068   Code   RO         3292    i.RCC_AHB1PeriphClockCmd  stm32f4xx_rcc.o
    0x0800326c   0x0800326c   0x00000068   Code   RO         3304    i.RCC_APB2PeriphClockCmd  stm32f4xx_rcc.o
    0x080032d4   0x080032d4   0x00000064   Code   RO         3312    i.RCC_DeInit        stm32f4xx_rcc.o
    0x08003338   0x08003338   0x000000a4   Code   RO         3314    i.RCC_GetFlagStatus  stm32f4xx_rcc.o
    0x080033dc   0x080033dc   0x00000010   Code   RO         3316    i.RCC_GetSYSCLKSource  stm32f4xx_rcc.o
    0x080033ec   0x080033ec   0x00000060   Code   RO         3317    i.RCC_HCLKConfig    stm32f4xx_rcc.o
    0x0800344c   0x0800344c   0x00000044   Code   RO         3318    i.RCC_HSEConfig     stm32f4xx_rcc.o
    0x08003490   0x08003490   0x00000058   Code   RO         3328    i.RCC_PCLK1Config   stm32f4xx_rcc.o
    0x080034e8   0x080034e8   0x00000058   Code   RO         3329    i.RCC_PCLK2Config   stm32f4xx_rcc.o
    0x08003540   0x08003540   0x00000038   Code   RO         3330    i.RCC_PLLCmd        stm32f4xx_rcc.o
    0x08003578   0x08003578   0x000000b8   Code   RO         3331    i.RCC_PLLConfig     stm32f4xx_rcc.o
    0x08003630   0x08003630   0x00000048   Code   RO         3342    i.RCC_SYSCLKConfig  stm32f4xx_rcc.o
    0x08003678   0x08003678   0x00000038   Code   RO         3344    i.RCC_WaitForHSEStartUp  stm32f4xx_rcc.o
    0x080036b0   0x080036b0   0x00000002   Code   RO          246    i.SVC_Handler       stm32f4xx_it.o
    0x080036b2   0x080036b2   0x00000002   PAD
    0x080036b4   0x080036b4   0x000000ec   Code   RO          346    i.SetSysClock       system_stm32f4xx.o
    0x080037a0   0x080037a0   0x0000002c   Code   RO         6288    i.SignalGenerator_ClearInputBuffer  signal_generator.o
    0x080037cc   0x080037cc   0x000000ac   Code   RO         6290    i.SignalGenerator_DisplayAmpSet  signal_generator.o
    0x08003878   0x08003878   0x0000008c   Code   RO         6291    i.SignalGenerator_DisplayFreqSet  signal_generator.o
    0x08003904   0x08003904   0x00000108   Code   RO         6292    i.SignalGenerator_DisplayMain  signal_generator.o
    0x08003a0c   0x08003a0c   0x000000ec   Code   RO         6293    i.SignalGenerator_DisplayMeasurement  signal_generator.o
    0x08003af8   0x08003af8   0x00000078   Code   RO         6294    i.SignalGenerator_DisplayWaveSet  signal_generator.o
    0x08003b70   0x08003b70   0x00000038   Code   RO         6297    i.SignalGenerator_Init  signal_generator.o
    0x08003ba8   0x08003ba8   0x00000024   Code   RO         6299    i.SignalGenerator_ParseAmplitude  signal_generator.o
    0x08003bcc   0x08003bcc   0x00000024   Code   RO         6300    i.SignalGenerator_ParseFrequency  signal_generator.o
    0x08003bf0   0x08003bf0   0x00000148   Code   RO         6301    i.SignalGenerator_Process  signal_generator.o
    0x08003d38   0x08003d38   0x00000098   Code   RO         6302    i.SignalGenerator_ProcessDigitInput  signal_generator.o
    0x08003dd0   0x08003dd0   0x0000012c   Code   RO         6303    i.SignalGenerator_ProcessKeypad1  signal_generator.o
    0x08003efc   0x08003efc   0x00000094   Code   RO         6304    i.SignalGenerator_ProcessKeypad2  signal_generator.o
    0x08003f90   0x08003f90   0x00000034   Code   RO         6305    i.SignalGenerator_SetAmplitude  signal_generator.o
    0x08003fc4   0x08003fc4   0x00000034   Code   RO         6306    i.SignalGenerator_SetFrequency  signal_generator.o
    0x08003ff8   0x08003ff8   0x0000002c   Code   RO         6307    i.SignalGenerator_SetWaveType  signal_generator.o
    0x08004024   0x08004024   0x00000058   Code   RO         6309    i.SignalGenerator_UpdateDisplay  signal_generator.o
    0x0800407c   0x0800407c   0x00000048   Code   RO         6310    i.SignalGenerator_UpdateParameters  signal_generator.o
    0x080040c4   0x080040c4   0x00000048   Code   RO         5526    i.SysTick_GetCalibratedDelay  systick.o
    0x0800410c   0x0800410c   0x0000000c   Code   RO         5528    i.SysTick_GetTick   systick.o
    0x08004118   0x08004118   0x0000001c   Code   RO          247    i.SysTick_Handler   stm32f4xx_it.o
    0x08004134   0x08004134   0x00000038   Code   RO         5531    i.SysTick_Handler_Internal  systick.o
    0x0800416c   0x0800416c   0x0000007c   Code   RO         5532    i.SysTick_Init      systick.o
    0x080041e8   0x080041e8   0x00000020   Code   RO         5535    i.SysTick_ResetStats  systick.o
    0x08004208   0x08004208   0x00000060   Code   RO         5537    i.SysTick_UpdateStats  systick.o
    0x08004268   0x08004268   0x0000006a   Code   RO           16    i.SystemClock_Config  main.o
    0x080042d2   0x080042d2   0x00000002   PAD
    0x080042d4   0x080042d4   0x00000068   Code   RO          348    i.SystemInit        system_stm32f4xx.o
    0x0800433c   0x0800433c   0x00000016   Code   RO          248    i.TIM2_IRQHandler   stm32f4xx_it.o
    0x08004352   0x08004352   0x00000002   Code   RO          249    i.TIM6_DAC_IRQHandler  stm32f4xx_it.o
    0x08004354   0x08004354   0x000000b8   Code   RO         4698    i.TIM_ClearITPendingBit  stm32f4xx_tim.o
    0x0800440c   0x0800440c   0x00000108   Code   RO         4724    i.TIM_GetITStatus   stm32f4xx_tim.o
    0x08004514   0x08004514   0x00000002   Code   RO           17    i.TimingDelay_Decrement  main.o
    0x08004516   0x08004516   0x00000002   Code   RO          250    i.USART2_IRQHandler  stm32f4xx_it.o
    0x08004518   0x08004518   0x00000010   Code   RO         5937    i.UpdateSystemTime  ad9910_control.o
    0x08004528   0x08004528   0x00000004   Code   RO          251    i.UsageFault_Handler  stm32f4xx_it.o
    0x0800452c   0x0800452c   0x00000020   Code   RO         5938    i.ValidateAmplitude  ad9910_control.o
    0x0800454c   0x0800454c   0x00000020   Code   RO         5939    i.ValidateFrequency  ad9910_control.o
    0x0800456c   0x0800456c   0x00000058   Code   RO         5940    i.ValidateGainFactor  ad9910_control.o
    0x080045c4   0x080045c4   0x00000030   Code   RO         7150    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x080045f4   0x080045f4   0x0000007a   Code   RO         6973    i.__hardfp_sqrt     m_wm.l(sqrt.o)
    0x0800466e   0x0800466e   0x0000000e   Code   RO         6870    i._is_digit         c_w.l(__printf_wp.o)
    0x0800467c   0x0800467c   0x00000004   Code   RO           18    i.assert_failed     main.o
    0x08004680   0x08004680   0x000000fc   Code   RO           19    i.main              main.o
    0x0800477c   0x0800477c   0x0000002c   Code   RO         7139    locale$$code        c_w.l(lc_ctype_c.o)
    0x080047a8   0x080047a8   0x0000002c   Code   RO         7142    locale$$code        c_w.l(lc_numeric_c.o)
    0x080047d4   0x080047d4   0x00000150   Code   RO         6913    x$fpl$dadd          fz_wm.l(daddsub_clz.o)
    0x08004924   0x08004924   0x00000018   Code   RO         7024    x$fpl$dcmpinf       fz_wm.l(dcmpi.o)
    0x0800493c   0x0800493c   0x000002b0   Code   RO         6920    x$fpl$ddiv          fz_wm.l(ddiv.o)
    0x08004bec   0x08004bec   0x0000005a   Code   RO         6923    x$fpl$dfixu         fz_wm.l(dfixu.o)
    0x08004c46   0x08004c46   0x0000002e   Code   RO         6928    x$fpl$dflt          fz_wm.l(dflt_clz.o)
    0x08004c74   0x08004c74   0x00000026   Code   RO         6927    x$fpl$dfltu         fz_wm.l(dflt_clz.o)
    0x08004c9a   0x08004c9a   0x00000002   PAD
    0x08004c9c   0x08004c9c   0x00000078   Code   RO         6933    x$fpl$dleqf         fz_wm.l(dleqf.o)
    0x08004d14   0x08004d14   0x00000154   Code   RO         6935    x$fpl$dmul          fz_wm.l(dmul.o)
    0x08004e68   0x08004e68   0x0000009c   Code   RO         7026    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x08004f04   0x08004f04   0x0000000c   Code   RO         7028    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x08004f10   0x08004f10   0x0000006c   Code   RO         6937    x$fpl$drleqf        fz_wm.l(drleqf.o)
    0x08004f7c   0x08004f7c   0x00000016   Code   RO         6914    x$fpl$drsb          fz_wm.l(daddsub_clz.o)
    0x08004f92   0x08004f92   0x00000002   PAD
    0x08004f94   0x08004f94   0x00000198   Code   RO         7030    x$fpl$dsqrt         fz_wm.l(dsqrt_umaal.o)
    0x0800512c   0x0800512c   0x000001d4   Code   RO         6915    x$fpl$dsub          fz_wm.l(daddsub_clz.o)
    0x08005300   0x08005300   0x00000056   Code   RO         6939    x$fpl$f2d           fz_wm.l(f2d.o)
    0x08005356   0x08005356   0x0000008c   Code   RO         7032    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x080053e2   0x080053e2   0x0000000a   Code   RO         7221    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x080053ec   0x080053ec   0x00000004   Code   RO         6941    x$fpl$printf1       fz_wm.l(printf1.o)
    0x080053f0   0x080053f0   0x00000000   Code   RO         7034    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x080053f0   0x080053f0   0x0000000c   Data   RO         5760    .constdata          ad9910_waveform.o
    0x080053fc   0x080053fc   0x00000040   Data   RO         6467    .constdata          matrix_keypad.o
    0x0800543c   0x0800543c   0x000003b0   Data   RO         6818    .constdata          oled_font.o
    0x080057ec   0x080057ec   0x00000094   Data   RO         7114    .constdata          c_w.l(bigflt0.o)
    0x08005880   0x08005880   0x00000148   Data   RO           21    .conststring        main.o
    0x080059c8   0x080059c8   0x00000010   Data   RO         6312    .conststring        signal_generator.o
    0x080059d8   0x080059d8   0x00000091   Data   RO         6468    .conststring        matrix_keypad.o
    0x08005a69   0x08005a69   0x00000003   PAD
    0x08005a6c   0x08005a6c   0x00000020   Data   RO         7350    Region$$Table       anon$$obj.o
    0x08005a8c   0x08005a8c   0x00000110   Data   RO         7138    locale$$data        c_w.l(lc_ctype_c.o)
    0x08005b9c   0x08005b9c   0x0000001c   Data   RO         7141    locale$$data        c_w.l(lc_numeric_c.o)


    Execution Region RW_IRAM2 (Exec base: 0x10000000, Load base: 0x08005c64, Size: 0x00000000, Max: 0x00010000, ABSOLUTE)

    **** No section assigned to this execution region ****


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08005bb8, Size: 0x00000fc0, Max: 0x00020000, ABSOLUTE, COMPRESSED[0x000000ac])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000004   Data   RW           22    .data               main.o
    0x20000004   COMPRESSED   0x00000014   Data   RW          349    .data               system_stm32f4xx.o
    0x20000018   COMPRESSED   0x00000019   Data   RW         5539    .data               systick.o
    0x20000031   COMPRESSED   0x00000007   PAD
    0x20000038   COMPRESSED   0x0000001a   Data   RW         5761    .data               ad9910_waveform.o
    0x20000052   COMPRESSED   0x00000006   PAD
    0x20000058   COMPRESSED   0x00000025   Data   RW         5942    .data               ad9910_control.o
    0x2000007d   COMPRESSED   0x00000001   PAD
    0x2000007e   COMPRESSED   0x00000002   Data   RW         6079    .data               command_parser.o
    0x20000080   COMPRESSED   0x00000001   Data   RW         6177    .data               gain_calculator.o
    0x20000081   COMPRESSED   0x00000003   PAD
    0x20000084   COMPRESSED   0x0000004a   Data   RW         6313    .data               signal_generator.o
    0x200000ce   COMPRESSED   0x00000002   PAD
    0x200000d0   COMPRESSED   0x0000008c   Data   RW         6469    .data               matrix_keypad.o
    0x2000015c        -       0x00000200   Zero   RW           20    .bss                main.o
    0x2000035c        -       0x00000010   Zero   RW         5538    .bss                systick.o
    0x2000036c        -       0x0000000c   Zero   RW         5759    .bss                ad9910_waveform.o
    0x20000378        -       0x00000118   Zero   RW         5941    .bss                ad9910_control.o
    0x20000490        -       0x00000080   Zero   RW         6078    .bss                command_parser.o
    0x20000510        -       0x00000038   Zero   RW         6175    .bss                gain_calculator.o
    0x20000548        -       0x00000014   Zero   RW         6311    .bss                signal_generator.o
    0x2000055c        -       0x00000400   Zero   RW         6656    .bss                oled.o
    0x2000095c        -       0x00000060   Zero   RW         7079    .bss                c_w.l(libspace.o)
    0x200009bc   COMPRESSED   0x00000004   PAD
    0x200009c0        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f40_41xxx.o
    0x20000bc0        -       0x00000400   Zero   RW            1    STACK               startup_stm32f40_41xxx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       910        146          0         37        280      10731   ad9910_control.o
       520         40          0          0          0       7937   ad9910_hal.o
      1390        258         12         26         12      12151   ad9910_waveform.o
        44          0          0          0          0        459   bsp.o
        28         10          0          2        128       2421   command_parser.o
       608        116          0          1         56       4613   gain_calculator.o
       664        146        328          4        512      18329   main.o
       606         28        209        140          0       7114   matrix_keypad.o
         0          0          0          0          0     236704   misc.o
      1036         44          0          0       1024       7323   oled.o
        32          6        944          0          0       1139   oled_font.o
      2340        468         16         74         20      15889   signal_generator.o
        64         26        392          0       1536        836   startup_stm32f40_41xxx.o
         0          0          0          0          0      15356   stm32f4xx_dfsdm.o
       116         32          0          0          0        615   stm32f4xx_flash.o
      1044        290          0          0          0      10232   stm32f4xx_gpio.o
        86          6          0          0          0       5925   stm32f4xx_it.o
      1196        328          0          0          0       9718   stm32f4xx_rcc.o
       448        154          0          0          0      21666   stm32f4xx_tim.o
       340         32          0         20          0       1737   system_stm32f4xx.o
       648        104          0         25         16      33750   systick.o

    ----------------------------------------------------------------------
     12140       <USER>       <GROUP>        348       3584     424645   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        20          0          3         19          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        44          6          0          0          0         84   __2sprintf.o
        90          0          0          0          0          0   __dczerorl2.o
         8          0          0          0          0         68   __main.o
       352          0          0          0          0         88   __printf_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        28          0          0          0          0          0   __scatter_zi.o
        28          0          0          0          0         68   _chval.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       128         16          0          0          0         84   _printf_fp_infnan.o
       178          0          0          0          0         88   _printf_intcommon.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
         6          0          0          0          0          0   _printf_u.o
        22          0          0          0          0        100   _rserrno.o
        10          0          0          0          0         68   _sputc.o
       158          0          0          0          0         92   _strtoul.o
        26          0          0          0          0         80   atoi.o
        26          0          0          0          0         80   atol.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        34          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        72          0          0          0          0         80   strcpy.o
        62          0          0          0          0         76   strlen.o
       112          0          0          0          0         88   strtol.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
       826         16          0          0          0        492   daddsub_clz.o
        24          0          0          0          0        116   dcmpi.o
       688        140          0          0          0        256   ddiv.o
        90          4          0          0          0        140   dfixu.o
        84          0          0          0          0        232   dflt_clz.o
       120          4          0          0          0        140   dleqf.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
       108          0          0          0          0        128   drleqf.o
       408         56          0          0          0        168   dsqrt_umaal.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
        10          0          0          0          0        116   fpinit.o
         4          0          0          0          0        116   printf1.o
         0          0          0          0          0          0   usenofp.o
        48          0          0          0          0        124   fpclassify.o
       122          0          0          0          0        148   sqrt.o

    ----------------------------------------------------------------------
      8956        <USER>        <GROUP>          0        100       6596   Library Totals
        18          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      5672        224        448          0         96       3748   c_w.l
      3096        244          0          0          0       2576   fz_wm.l
       170          0          0          0          0        272   m_wm.l

    ----------------------------------------------------------------------
      8956        <USER>        <GROUP>          0        100       6596   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     21096       2702       2384        348       3684     415009   Grand Totals
     21096       2702       2384        172       3684     415009   ELF Image Totals (compressed)
     21096       2702       2384        172          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                23480 (  22.93kB)
    Total RW  Size (RW Data + ZI Data)              4032 (   3.94kB)
    Total ROM Size (Code + RO Data + RW Data)      23652 (  23.10kB)

==============================================================================


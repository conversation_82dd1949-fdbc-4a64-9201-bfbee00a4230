/**
  ******************************************************************************
  * @file    oled.h
  * <AUTHOR>
  * @version V2.0
  * @date    2024
  * @brief   I2C OLED显示模块 - 0.96寸四针脚IIC通信OLED驱动
  *          基于SSD1306控制器的0.96寸I2C OLED屏幕驱动
  ******************************************************************************
  * @attention
  * 
  * 硬件规格：
  * - 屏幕尺寸：0.96寸 128x64像素
  * - 控制器：SSD1306
  * - 接口类型：4引脚I2C接口（VCC, GND, SCL, SDA）
  * - STM32连接：使用软件模拟I2C
  *   - SCL -> PB6
  *   - SDA -> PB7
  * 
  * 功能特性：
  * - 基于软件模拟I2C通信
  * - 完整的SSD1306初始化序列
  * - 基础显示功能：清屏、字符、字符串、数字
  * - 简洁稳定的驱动实现
  *
  ******************************************************************************
  */

#ifndef __OLED_H
#define __OLED_H 

#include "stm32f4xx.h"
#include "stdlib.h"	

#ifndef u8
#define u8 uint8_t
#endif

#ifndef u16
#define u16 uint16_t
#endif

#ifndef u32
#define u32 uint32_t
#endif

//-----------------OLED端口定义---------------- 

#define OLED_SCL_Clr() GPIO_ResetBits(GPIOB,GPIO_Pin_6)//SCL
#define OLED_SCL_Set() GPIO_SetBits(GPIOB,GPIO_Pin_6)

#define OLED_SDA_Clr() GPIO_ResetBits(GPIOB,GPIO_Pin_7)//SDA
#define OLED_SDA_Set() GPIO_SetBits(GPIOB,GPIO_Pin_7)

#define OLED_CMD  0	//写命令
#define OLED_DATA 1	//写数据

//OLED控制用函数
void OLED_WR_Byte(u8 dat,u8 cmd);	    
void OLED_Display_On(void);
void OLED_Display_Off(void);	   							   		    
void OLED_Init(void);
void OLED_Clear(void);
void OLED_DrawPoint(u8 x,u8 y,u8 t);
void OLED_Fill(u8 x1,u8 y1,u8 x2,u8 y2,u8 dot);
void OLED_ShowChar(u8 x,u8 y,u8 chr);
void OLED_ShowNum(u8 x,u8 y,u32 num,u8 len,u8 size);
void OLED_ShowString(u8 x,u8 y,u8 *p);
void OLED_Set_Pos(unsigned char x, unsigned char y);
void OLED_ShowCHinese(u8 x,u8 y,u8 no);
void OLED_DrawBMP(unsigned char x0, unsigned char y0,unsigned char x1, unsigned char y1,unsigned char BMP[]);
void Delay_50ms(unsigned int Del_50ms);
void Delay_1ms(unsigned int Del_1ms);
void fill_picture(unsigned char fill_Data);
void Picture(void);
void IIC_Start(void);
void IIC_Stop(void);
void Write_IIC_Command(unsigned char IIC_Command);
void Write_IIC_Data(unsigned char IIC_Data);
void Write_IIC_Byte(unsigned char IIC_Byte);
u8 IIC_Wait_Ack(void);

//OLED显示测试函数
void OLED_Test_Display(void);

//OLED诊断函数
void OLED_Diagnostic_Test(void);

//OLED简单显示测试
void OLED_Simple_Test(void);

//强力清屏函数
void OLED_Force_Clear(void);

//OLED重新初始化显示设置
void OLED_Reset_Display(void);

//数学函数
u32 mypow(u8 m,u8 n);

#endif


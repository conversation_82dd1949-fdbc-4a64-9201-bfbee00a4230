/**
  ******************************************************************************
  * @file    matrix_keypad.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024
  * @brief   双4x4矩阵键盘驱动模块实现
  ******************************************************************************
  */

#include "matrix_keypad.h"
#include "../Core/systick.h"
#include <stddef.h>  // 包含NULL定义

/* Private typedef -----------------------------------------------------------*/

/**
  * @brief  键盘配置结构体
  */
typedef struct {
    GPIO_TypeDef* row_port;         ///< 行端口
    uint16_t row_pins;              ///< 行引脚
    GPIO_TypeDef* col_port;         ///< 列端口
    uint16_t col_pins;              ///< 列引脚
    uint16_t row_pin_array[4];      ///< 行引脚数组
    uint16_t col_pin_array[4];      ///< 列引脚数组
} Keypad_Config_t;

/* Private define ------------------------------------------------------------*/

#define KEYPAD_INVALID_KEY          0xFF

/* Private macro -------------------------------------------------------------*/

/* Private variables ---------------------------------------------------------*/

/**
  * @brief  键盘初始化标志
  */
volatile bool g_keypad_initialized = false;

/**
  * @brief  键盘配置数组
  */
static const Keypad_Config_t s_keypad_configs[KEYPAD_COUNT] = {
    // 第一块键盘配置
    {
        .row_port = KEYPAD1_ROW_PORT,
        .row_pins = KEYPAD1_ROW_PINS,
        .col_port = KEYPAD1_COL_PORT,
        .col_pins = KEYPAD1_COL_PINS,
        .row_pin_array = {GPIO_Pin_0, GPIO_Pin_1, GPIO_Pin_2, GPIO_Pin_3},
        .col_pin_array = {GPIO_Pin_4, GPIO_Pin_5, GPIO_Pin_6, GPIO_Pin_7}
    },
    // 第二块键盘配置
    {
        .row_port = KEYPAD2_ROW_PORT,
        .row_pins = KEYPAD2_ROW_PINS,
        .col_port = KEYPAD2_COL_PORT,
        .col_pins = KEYPAD2_COL_PINS,
        .row_pin_array = {GPIO_Pin_8, GPIO_Pin_9, GPIO_Pin_10, GPIO_Pin_11},
        .col_pin_array = {GPIO_Pin_12, GPIO_Pin_13, GPIO_Pin_14, GPIO_Pin_15}
    }
};

/**
  * @brief  按键状态数组 (预留用于高级消抖)
  */
// static Key_State_t s_key_states[KEYPAD_COUNT][KEYPAD_ROWS][KEYPAD_COLS];

/**
  * @brief  消抖计数器 (预留用于高级消抖)
  */
// static uint32_t s_debounce_counters[KEYPAD_COUNT][KEYPAD_ROWS][KEYPAD_COLS];

/**
  * @brief  第一块键盘按键名称表
  */
static const char* s_keypad1_names[17] = {
    "NONE", "1", "2", "3", "F+",
    "4", "5", "6", "F-",
    "7", "8", "9", "A+", 
    "*", "0", "#", "A-"
};

/**
  * @brief  第二块键盘按键名称表 (简化版)
  */
static const char* s_keypad2_names[17] = {
    "NONE", "FREQ", "AMP", "<<", ">>",
    "RSV5", "RSV6", "RSV7", "RSV8",
    "SIN", "SQR", "TRI", "SAW",
    "RSV13", "RSV14", "RSV15", "RSV16"
};

/* Private function prototypes -----------------------------------------------*/

static void MatrixKeypad_GPIO_Init(void);
static uint8_t MatrixKeypad_ScanSingle(Keypad_ID_t keypad_id);
static void MatrixKeypad_SetRowOutput(Keypad_ID_t keypad_id, uint8_t row, bool state);
static bool MatrixKeypad_ReadColumn(Keypad_ID_t keypad_id, uint8_t col);

/* Exported functions --------------------------------------------------------*/

/**
  * @brief  矩阵键盘初始化
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t MatrixKeypad_Init(void)
{
    if (g_keypad_initialized) {
        return 0; // 已经初始化
    }
    
    // GPIO初始化
    MatrixKeypad_GPIO_Init();
    
    // 清空状态数组 (当前使用简单消抖，高级消抖功能已注释)
    // for (uint8_t keypad = 0; keypad < KEYPAD_COUNT; keypad++) {
    //     for (uint8_t row = 0; row < KEYPAD_ROWS; row++) {
    //         for (uint8_t col = 0; col < KEYPAD_COLS; col++) {
    //             s_key_states[keypad][row][col] = KEY_STATE_RELEASED;
    //             s_debounce_counters[keypad][row][col] = 0;
    //         }
    //     }
    // }
    
    g_keypad_initialized = true;
    return 0;
}

/**
  * @brief  矩阵键盘反初始化
  * @param  None
  * @retval None
  */
void MatrixKeypad_DeInit(void)
{
    if (!g_keypad_initialized) {
        return;
    }
    
    // 这里可以添加GPIO反初始化代码
    
    g_keypad_initialized = false;
}

/**
  * @brief  扫描指定键盘
  * @param  keypad_id: 键盘编号
  * @retval 按键编码 (0表示无按键)
  */
uint8_t MatrixKeypad_Scan(Keypad_ID_t keypad_id)
{
    if (!g_keypad_initialized || keypad_id >= KEYPAD_COUNT) {
        return 0;
    }
    
    return MatrixKeypad_ScanSingle(keypad_id);
}

/**
  * @brief  扫描所有键盘
  * @param  event: 按键事件结构体指针
  * @retval true: 有按键事件, false: 无按键事件
  */
bool MatrixKeypad_ScanAll(Key_Event_t* event)
{
    if (!g_keypad_initialized || event == NULL) {
        return false;
    }
    
    // 扫描第一块键盘
    uint8_t key1 = MatrixKeypad_ScanSingle(KEYPAD_1);
    if (key1 != 0) {
        event->keypad_id = KEYPAD_1;
        event->key_code = key1;
        event->state = KEY_STATE_PRESSED;
        event->timestamp = SysTick_GetTick();
        return true;
    }
    
    // 扫描第二块键盘
    uint8_t key2 = MatrixKeypad_ScanSingle(KEYPAD_2);
    if (key2 != 0) {
        event->keypad_id = KEYPAD_2;
        event->key_code = key2;
        event->state = KEY_STATE_PRESSED;
        event->timestamp = SysTick_GetTick();
        return true;
    }
    
    return false;
}

/**
  * @brief  获取按键名称字符串
  * @param  keypad_id: 键盘编号
  * @param  key_code: 按键编码
  * @retval 按键名称字符串
  */
const char* MatrixKeypad_GetKeyName(Keypad_ID_t keypad_id, uint8_t key_code)
{
    if (keypad_id >= KEYPAD_COUNT || key_code > 16) {
        return "INVALID";
    }
    
    if (keypad_id == KEYPAD_1) {
        return s_keypad1_names[key_code];
    } else {
        return s_keypad2_names[key_code];
    }
}

/**
  * @brief  检查键盘是否就绪
  * @param  None
  * @retval true: 就绪, false: 未就绪
  */
bool MatrixKeypad_IsReady(void)
{
    return g_keypad_initialized;
}

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  GPIO初始化
  * @param  None
  * @retval None
  */
static void MatrixKeypad_GPIO_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    // 使能时钟 (两块键盘都使用GPIOE)
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOE, ENABLE);

    // 第一块键盘行线配置 (PE0-PE3, 输出)
    GPIO_InitStructure.GPIO_Pin = KEYPAD1_ROW_PINS;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(KEYPAD1_ROW_PORT, &GPIO_InitStructure);

    // 第一块键盘列线配置 (PE4-PE7, 输入上拉)
    GPIO_InitStructure.GPIO_Pin = KEYPAD1_COL_PINS;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(KEYPAD1_COL_PORT, &GPIO_InitStructure);

    // 第二块键盘行线配置 (PE8-PE11, 输出)
    GPIO_InitStructure.GPIO_Pin = KEYPAD2_ROW_PINS;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(KEYPAD2_ROW_PORT, &GPIO_InitStructure);

    // 第二块键盘列线配置 (PE12-PE15, 输入上拉)
    GPIO_InitStructure.GPIO_Pin = KEYPAD2_COL_PINS;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(KEYPAD2_COL_PORT, &GPIO_InitStructure);

    // 初始化所有行线为高电平
    GPIO_SetBits(KEYPAD1_ROW_PORT, KEYPAD1_ROW_PINS);
    GPIO_SetBits(KEYPAD2_ROW_PORT, KEYPAD2_ROW_PINS);
}

/**
  * @brief  扫描单个键盘
  * @param  keypad_id: 键盘编号
  * @retval 按键编码 (0表示无按键)
  */
static uint8_t MatrixKeypad_ScanSingle(Keypad_ID_t keypad_id)
{
    if (keypad_id >= KEYPAD_COUNT) {
        return 0;
    }

    uint8_t key_code = 0;

    // 逐行扫描
    for (uint8_t row = 0; row < KEYPAD_ROWS; row++) {
        // 设置当前行为低电平，其他行为高电平
        for (uint8_t r = 0; r < KEYPAD_ROWS; r++) {
            MatrixKeypad_SetRowOutput(keypad_id, r, (r != row));
        }

        // 短暂延时，等待电平稳定 (高频优化：进一步减少延时)
        for (volatile uint16_t i = 0; i < 20; i++);

        // 检查各列
        for (uint8_t col = 0; col < KEYPAD_COLS; col++) {
            if (!MatrixKeypad_ReadColumn(keypad_id, col)) {
                // 检测到按键按下（低电平）
                key_code = row * KEYPAD_COLS + col + 1;

                // 简单消抖：再次检测 (高频优化：大幅减少消抖延时)
                for (volatile uint16_t i = 0; i < 50; i++);
                if (!MatrixKeypad_ReadColumn(keypad_id, col)) {
                    // 恢复所有行为高电平
                    for (uint8_t r = 0; r < KEYPAD_ROWS; r++) {
                        MatrixKeypad_SetRowOutput(keypad_id, r, true);
                    }
                    return key_code;
                }
            }
        }
    }

    // 恢复所有行为高电平
    for (uint8_t r = 0; r < KEYPAD_ROWS; r++) {
        MatrixKeypad_SetRowOutput(keypad_id, r, true);
    }

    return 0; // 无按键按下
}

/**
  * @brief  设置行输出电平
  * @param  keypad_id: 键盘编号
  * @param  row: 行号 (0-3)
  * @param  state: 电平状态 (true=高电平, false=低电平)
  * @retval None
  */
static void MatrixKeypad_SetRowOutput(Keypad_ID_t keypad_id, uint8_t row, bool state)
{
    if (keypad_id >= KEYPAD_COUNT || row >= KEYPAD_ROWS) {
        return;
    }

    const Keypad_Config_t* config = &s_keypad_configs[keypad_id];
    uint16_t pin = config->row_pin_array[row];

    if (state) {
        GPIO_SetBits(config->row_port, pin);
    } else {
        GPIO_ResetBits(config->row_port, pin);
    }
}

/**
  * @brief  读取列输入电平
  * @param  keypad_id: 键盘编号
  * @param  col: 列号 (0-3)
  * @retval true: 高电平, false: 低电平
  */
static bool MatrixKeypad_ReadColumn(Keypad_ID_t keypad_id, uint8_t col)
{
    if (keypad_id >= KEYPAD_COUNT || col >= KEYPAD_COLS) {
        return true;
    }

    const Keypad_Config_t* config = &s_keypad_configs[keypad_id];
    uint16_t pin = config->col_pin_array[col];

    return (GPIO_ReadInputDataBit(config->col_port, pin) == SET);
}

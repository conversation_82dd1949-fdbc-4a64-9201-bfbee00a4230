# 双4x4矩阵键盘信号发生器系统设计文档

## 📋 概述

本系统设计了两个4x4矩阵键盘，配合OLED显示屏，实现完整的信号发生器控制功能，支持频率设置、峰峰值调节、波形选择和未知电路测量。

## 🔌 硬件连接配置

### 第一块4x4矩阵键盘 (数字输入键盘)
**连接到STM32F407的PE端口**

| 功能 | STM32引脚 | 连接说明 |
|------|-----------|----------|
| 行线0 | PE0 | 输出，推挽，控制第1行 |
| 行线1 | PE1 | 输出，推挽，控制第2行 |
| 行线2 | PE2 | 输出，推挽，控制第3行 |
| 行线3 | PE3 | 输出，推挽，控制第4行 |
| 列线0 | PE4 | 输入，上拉，检测第1列 |
| 列线1 | PE5 | 输入，上拉，检测第2列 |
| 列线2 | PE6 | 输入，上拉，检测第3列 |
| 列线3 | PE7 | 输入，上拉，检测第4列 |

### 第二块4x4矩阵键盘 (功能控制键盘)
**连接到STM32F407的PF端口**

| 功能 | STM32引脚 | 连接说明 |
|------|-----------|----------|
| 行线0 | PF0 | 输出，推挽，控制第1行 |
| 行线1 | PF1 | 输出，推挽，控制第2行 |
| 行线2 | PF2 | 输出，推挽，控制第3行 |
| 行线3 | PF3 | 输出，推挽，控制第4行 |
| 列线0 | PF4 | 输入，上拉，检测第1列 |
| 列线1 | PF5 | 输入，上拉，检测第2列 |
| 列线2 | PF6 | 输入，上拉，检测第3列 |
| 列线3 | PF7 | 输入，上拉，检测第4列 |

### OLED显示屏连接
**已配置的I2C OLED (0.96寸)**

| 功能 | STM32引脚 | 连接说明 |
|------|-----------|----------|
| SCL | PB6 | I2C时钟线，开漏输出 |
| SDA | PB7 | I2C数据线，开漏输出 |
| VCC | 3.3V | 电源正极 |
| GND | GND | 电源负极 |

## 🎹 键盘布局设计

### 第一块键盘 - 数字输入键盘
```
+-----+-----+-----+-----+
|  1  |  2  |  3  | F+  |
+-----+-----+-----+-----+
|  4  |  5  |  6  | F-  |
+-----+-----+-----+-----+
|  7  |  8  |  9  | A+  |
+-----+-----+-----+-----+
|  *  |  0  |  #  | A-  |
+-----+-----+-----+-----+
```

**功能说明：**
- **数字键 (1-9, 0)**: 用于数值输入
- **F+**: 频率增加 (步长100Hz)
- **F-**: 频率减少 (步长100Hz)
- **A+**: 幅度增加 (步长0.1V)
- **A-**: 幅度减少 (步长0.1V)
- **\***: 清除当前输入
- **#**: 确认输入

### 第二块键盘 - 功能控制键盘
```
+-----+-----+-----+-----+
|FREQ | AMP |WAVE | MSR |
+-----+-----+-----+-----+
| <<  | >>  | UP  | DN  |
+-----+-----+-----+-----+
| SIN | SQR | TRI | SAW |
+-----+-----+-----+-----+
| SET | CLR | ENT | ESC |
+-----+-----+-----+-----+
```

**功能说明：**
- **FREQ**: 进入频率设置模式
- **AMP**: 进入幅度设置模式
- **WAVE**: 进入波形选择模式
- **MSR**: 进入测量模式
- **<<**: 左移位 (数字编辑时)
- **>>**: 右移位 (数字编辑时)
- **UP**: 数值增加
- **DN**: 数值减少
- **SIN**: 选择正弦波
- **SQR**: 选择方波
- **TRI**: 选择三角波
- **SAW**: 选择锯齿波
- **SET**: 设置/确认
- **CLR**: 清除
- **ENT**: 确认/进入
- **ESC**: 退出/取消

## 🖥️ OLED显示界面设计

### 主界面 (正常模式)
```
SIGNAL GEN
F:1000Hz
A:1.0V
W:SIN
```

### 频率设置界面
```
FREQ SET
F:1000_
# CONFIRM
ESC EXIT
```

### 幅度设置界面
```
AMP SET
A:1.0_
# CONFIRM
ESC EXIT
```

### 波形选择界面
```
WAVE SEL
W:SIN
SIN SQR TRI
ENT EXIT
```

### 测量界面
```
MEASURE
F:1000Hz
G:-3.0dB
P:45.0°
```

## ⚙️ 工作模式说明

### 1. 正常模式 (MODE_NORMAL)
- 显示当前频率、幅度、波形
- 支持快速调节 (F+/F-/A+/A-)
- 支持直接波形切换

### 2. 频率设置模式 (MODE_FREQ_SET)
- 数字键盘输入频率值
- 支持1Hz - 100MHz范围
- #键确认，ESC键退出

### 3. 幅度设置模式 (MODE_AMP_SET)
- 数字键盘输入幅度值 (mV)
- 支持0.1V - 5.0V范围
- #键确认，ESC键退出

### 4. 波形选择模式 (MODE_WAVE_SEL)
- 直接按键选择波形类型
- 支持正弦波、方波、三角波、锯齿波
- ENT键确认，ESC键退出

### 5. 测量模式 (MODE_MEASURE)
- 显示输入信号参数
- 显示增益和相位信息
- 用于未知电路特性测量

## 🔧 技术特性

### 扫描机制
- **扫描方式**: 行扫描法
- **消抖处理**: 软件消抖，20ms消抖时间
- **扫描频率**: 100Hz (10ms间隔)
- **检测方式**: 低电平导通

### 参数范围
- **频率范围**: 1Hz - 100MHz
- **幅度范围**: 0.1V - 5.0V (峰峰值)
- **频率步长**: 100Hz (快速调节)
- **幅度步长**: 0.1V (快速调节)

### 显示更新
- **更新频率**: 10Hz (100ms间隔)
- **显示分辨率**: 128x64像素
- **字符大小**: 8x16点阵

## 📊 软件架构

### 模块组织
```
SignalGenerator (主控制模块)
├── MatrixKeypad (矩阵键盘驱动)
├── OLED (显示驱动)
├── AD9910_Control (DDS控制)
└── GainCalculator (增益计算)
```

### 主要文件
- `matrix_keypad.h/.c` - 矩阵键盘驱动
- `signal_generator.h/.c` - 信号发生器控制
- `oled.h/.c` - OLED显示驱动
- `oled_font.h/.c` - OLED字体库

## 🎯 使用流程

### 基本操作流程
1. **系统启动** → 显示启动信息
2. **进入主界面** → 显示当前参数
3. **选择操作模式** → 按功能键进入相应模式
4. **参数设置** → 使用数字键盘输入
5. **确认设置** → 按#或ENT确认
6. **返回主界面** → 按ESC或自动返回

### 快速调节流程
1. **在主界面** → 直接使用F+/F-/A+/A-
2. **实时调节** → 立即生效
3. **波形切换** → 直接按SIN/SQR/TRI/SAW

### 测量流程
1. **按MSR键** → 进入测量模式
2. **连接被测电路** → 系统自动测量
3. **查看结果** → 显示频率、增益、相位
4. **按ESC退出** → 返回主界面

## ⚠️ 注意事项

### 硬件注意事项
1. **矩阵键盘为低电平导通**，需要上拉电阻
2. **确保引脚连接正确**，避免短路
3. **OLED需要3.3V供电**，不要接5V
4. **键盘扫描会占用CPU时间**，主循环延时适当增加

### 软件注意事项
1. **键盘扫描频率不宜过高**，避免误触发
2. **消抖时间要合适**，太短会误触发，太长响应慢
3. **显示更新不宜过频繁**，避免闪烁
4. **参数范围检查**，防止超出AD9910能力范围

## 🚀 扩展功能

### 可扩展功能
1. **预设参数存储** - 保存常用参数组合
2. **扫频功能** - 自动扫描频率范围
3. **调制功能** - AM/FM/PM调制
4. **校准功能** - 幅度和频率校准
5. **数据记录** - 测量数据存储

### 接口预留
1. **串口通信** - 支持PC控制
2. **外部触发** - 支持外部信号触发
3. **同步输出** - 多通道同步
4. **状态指示** - LED状态显示

**系统已完成基础功能实现，可以进行编译测试。**

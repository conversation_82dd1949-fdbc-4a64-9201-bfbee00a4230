/**
  ******************************************************************************
  * @file    oled_font.h
  * <AUTHOR>
  * @version V1.0
  * @date    2024
  * @brief   OLED字体库 - 8x16点阵字符
  ******************************************************************************
  */

#ifndef __OLED_FONT_H
#define __OLED_FONT_H

#include "stm32f4xx.h"

// 8x16 ASCII字符字体库声明
// 每个字符占用16字节，8列x16行
extern const unsigned char F8X16[][16];

// 获取字符的字体数据
const unsigned char* OLED_GetCharFont(unsigned char chr);

#endif
